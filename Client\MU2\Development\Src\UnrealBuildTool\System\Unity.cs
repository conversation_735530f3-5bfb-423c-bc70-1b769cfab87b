/**
 * Copyright 1998-2014 Epic Games, Inc. All Rights Reserved.
 */

using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Diagnostics;

namespace UnrealBuildTool
{
	class Unity
	{
		/**
		 * Given a set of C++ files, generates another set of C++ files that #include all the original
		 * files, the goal being to compile the same code in fewer translation units.
		 * The "unity" files are written to the CompileEnvironment's OutputDirectory.
		 * @param CPPFiles - The C++ files to #include.
		 * @param CompileEnvironment - The environment that is used to compile the C++ files.
		 * @return The "unity" C++ files.
		 */
		public static List<FileItem> GenerateUnityCPPs(
			List<FileItem> CPPFiles, 
			CPPEnvironment CompileEnvironment
			)	
		{			
			// Figure out size of all input files combined. We use this to determine whether to use larger unity threshold or not.
			long TotalBytesInCPPFiles = 0;
			foreach( FileItem CPPFile in CPPFiles )
			{
				TotalBytesInCPPFiles += CPPFile.Length;
			}

			// We have an increased threshold for unity file size if, and only if, all files fit into the same unity file. This
			// is beneficial when dealing with PCH files. The default PCH creation limit is X unity files so if we generate X-1 this
			// could be fairly slow and we could group them all into the same unity file. We don't want to always use the higher
			// limit as this would push more projects below the X threshold so we only do this if we can go down to 1.
			bool bForceIntoSingleUnityFile = BuildConfiguration.bStressTestUnity;			
			// The 1.2 is slack as we can't split files in the middle.
			if( (TotalBytesInCPPFiles < (BuildConfiguration.NumIncludedBytesPerUnityCPP-1) * BuildConfiguration.MinFilesUsingPrecompiledHeader * 1.2) 
			// Optimization only makes sense if PCH files are enabled.
			&&	CompileEnvironment.ShouldUsePCHs() )
			{
				bForceIntoSingleUnityFile = true;
			}

			// Create a set of CPP files that combine smaller CPP files into larger compilation units, along with the corresponding 
			// actions to compile them.
			int InputFileIndex = 0;
			List<FileItem> UnityCPPFiles = new List<FileItem>();
			while (InputFileIndex < CPPFiles.Count)
			{
				StringWriter OutputUnityCPPWriter = new StringWriter();
				StringWriter OutputUnityCPPWriterExtra = null;
				// add an extra file for UBT to get the #include dependencies from
				if (CompileEnvironment.TargetPlatform == CPPTargetPlatform.IPhone ||
					CompileEnvironment.TargetPlatform == CPPTargetPlatform.Mac)
				{
					OutputUnityCPPWriterExtra = new StringWriter();
				}
				
				OutputUnityCPPWriter.WriteLine("// This file is automatically generated at compile-time to include some subset of the user-created cpp files.");

				// Explicitly include the precompiled header first, since Visual C++ expects the first top-level #include to be the header file
				// that was used to create the PCH.
				if (CompileEnvironment.PrecompiledHeaderIncludeFilename != null)
				{
					OutputUnityCPPWriter.WriteLine("#include \"{0}\"", CompileEnvironment.PrecompiledHeaderIncludeFilename);
					if (OutputUnityCPPWriterExtra != null)
					{
						OutputUnityCPPWriterExtra.WriteLine("#include \"{0}\"", CompileEnvironment.PrecompiledHeaderIncludeFilename);
					}
				}

				// Add source files to the unity file until the number of included bytes crosses a threshold.
				long NumIncludedBytesInThisOutputFile = 0;
                string FileDescription = "";
				while(	InputFileIndex < CPPFiles.Count &&
						(bForceIntoSingleUnityFile ||
						NumIncludedBytesInThisOutputFile < BuildConfiguration.NumIncludedBytesPerUnityCPP))
				{
					FileItem CPPFile = CPPFiles[InputFileIndex];
					if (CompileEnvironment.TargetPlatform == CPPTargetPlatform.IPhone)
					{
						OutputUnityCPPWriter.WriteLine("#include \"{0}\"", IPhoneToolChain.LocalToMacPath(CPPFile.AbsolutePath, true));
						OutputUnityCPPWriterExtra.WriteLine("#include \"{0}\"", CPPFile.AbsolutePath);
					}
					else if (CompileEnvironment.TargetPlatform == CPPTargetPlatform.Mac)
					{
						OutputUnityCPPWriter.WriteLine("#include \"{0}\"", MacToolChain.LocalToMacPath(CPPFile.AbsolutePath, true));
						OutputUnityCPPWriterExtra.WriteLine("#include \"{0}\"", CPPFile.AbsolutePath);
					}
					else
					{
						OutputUnityCPPWriter.WriteLine("#include \"{0}\"", CPPFile.AbsolutePath);
					}
					NumIncludedBytesInThisOutputFile += CPPFile.Length;
                    InputFileIndex++;
                    FileDescription += Path.GetFileName(CPPFile.AbsolutePath) + " + ";
				}
                // Remove trailing " + "
                FileDescription = FileDescription.Remove( FileDescription.Length - 3); 

				// Write the unity file to the intermediate folder.
				string UnityCPPFilePath = Path.Combine(
					CompileEnvironment.OutputDirectory,
					string.Format("Unity_{0}EtAl.cpp",Path.GetFileNameWithoutExtension(CPPFiles[InputFileIndex - 1].AbsolutePath))
					);
				FileItem UnityCPPFile = FileItem.CreateIntermediateTextFile(UnityCPPFilePath, OutputUnityCPPWriter.ToString());
				UnityCPPFile.RelativeCost = NumIncludedBytesInThisOutputFile;
				UnityCPPFile.Description = FileDescription;
                UnityCPPFiles.Add(UnityCPPFile);

				// write out the extra file
				if (OutputUnityCPPWriterExtra != null)
				{
					FileItem.CreateIntermediateTextFile(UnityCPPFilePath + ".ex", OutputUnityCPPWriterExtra.ToString());
				}
			}

			return UnityCPPFiles;
		}
	}
}