// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"
#include "../../Src/Launch/Resources/Version.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#include ""afxres.h""\r\n"
    "#include ""../../../../Shared/Version_RC.h""\0"
END

3 TEXTINCLUDE 
BEGIN
    "\r\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,ENGINE_VERSION,0
 PRODUCTVERSION 1,0,ENGINE_VERSION,0
 FILEFLAGSMASK 0x17L
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", EPIC_COMPANY_NAME
            VALUE "FileDescription", "UnrealLightmass"
            VALUE "InternalName", "Lightmass"
            VALUE "LegalCopyright", EPIC_COPYRIGHT_STRING
            VALUE "OriginalFilename", "UnrealLightmass.exe"
            VALUE "ProductName", EPIC_PRODUCT_NAME
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

/////////////////////////////////////////////////////////////////////////////
//
// Application Manifest
//

#if _DEBUG

// Debug manifest
#if _WIN64
1						RT_MANIFEST				"Debug-Win64.manifest"
#else
1						RT_MANIFEST				"Debug-Win32.manifest"
#endif

#else

// Release manifest
#if _WIN64
1						RT_MANIFEST				"Win64.manifest"
#else
1						RT_MANIFEST				"Win32.manifest"
#endif

#endif

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

