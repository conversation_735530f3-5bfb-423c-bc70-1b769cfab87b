<?xml version="1.0"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="RPCUtility.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <applicationSettings>
        <RPCUtility.Properties.Settings>
            <setting name="RPCPortName" serializeAs="String">
                <value>8099</value>
            </setting>
            <setting name="OverrideHostName" serializeAs="String">
                <value />
            </setting>
            <setting name="OverrideHostAddr" serializeAs="String">
                <value />
            </setting>
            <setting name="BaselineTaskMemorySize" serializeAs="String">
                <value>1000</value>
            </setting>
        </RPCUtility.Properties.Settings>
    </applicationSettings>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0,Profile=Client"/>
	</startup>
	<runtime>
		<generatePublisherEvidence enabled="false"/>
	</runtime>
</configuration>
