{"Version": 1, "WorkspaceRootPath": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{559F6F18-3B99-4A8B-A384-BCF2A9FD69EF}|Launch\\Launch.vcxproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\Launch\\Resources\\PCLaunch.rc||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{559F6F18-3B99-4A8B-A384-BCF2A9FD69EF}|Launch\\Launch.vcxproj|solutionrelative:Launch\\Resources\\PCLaunch.rc||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|f:\\release_branch\\client\\mu2\\development\\src\\unrealbuildtool\\toolchain\\vctoolchain.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\toolchain\\vctoolchain.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|f:\\release_branch\\client\\mu2\\development\\src\\unrealbuildtool\\toolchain\\inteltoolchain.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\toolchain\\inteltoolchain.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\configuration\\ue3buildmu2game.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\configuration\\ue3buildmu2game.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\configuration\\buildconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\configuration\\buildconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\toolchain\\xcodemactoolchain.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\toolchain\\xcodemactoolchain.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\android\\unrealbuildtool\\system\\androidtoolchain.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:android\\unrealbuildtool\\system\\androidtoolchain.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\system\\unity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\system\\unity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\system\\unrealbuildtool.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\system\\unrealbuildtool.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\system\\utils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\system\\utils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\system\\actiongraph.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\system\\actiongraph.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\system\\buildexception.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\system\\buildexception.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\system\\vcproject.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\system\\vcproject.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\configuration\\debuginfoheuristic.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\configuration\\debuginfoheuristic.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\configuration\\ue3buildconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\configuration\\ue3buildconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\configuration\\ue3buildexamplegame.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\configuration\\ue3buildexamplegame.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\configuration\\ue3buildtarget.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\configuration\\ue3buildtarget.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\unrealbuildtool\\configuration\\ue3buildwin32.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD7C5E1A-CFE4-4FD5-A525-1EB1599A39AC}|UnrealBuildTool\\UnrealBuildTool.csproj|solutionrelative:unrealbuildtool\\configuration\\ue3buildwin32.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EC63BC3B-61DC-4831-A12E-A1E5441D4A8B}|OnlineSubsystemSteamworks\\OnlineSubsystemSteamworks.vcxproj|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\OnlineSubsystemSteamworks\\Inc\\OnlineAsyncTaskManagerSteam.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{EC63BC3B-61DC-4831-A12E-A1E5441D4A8B}|OnlineSubsystemSteamworks\\OnlineSubsystemSteamworks.vcxproj|solutionrelative:OnlineSubsystemSteamworks\\Inc\\OnlineAsyncTaskManagerSteam.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Release_Branch\\Client\\MU2\\Development\\Src\\MU2Editor\\Inc\\MU2CompactShaderCache.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:MU2Editor\\Inc\\MU2CompactShaderCache.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "PCLaunch.rc", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\Launch\\Resources\\PCLaunch.rc", "RelativeDocumentMoniker": "Launch\\Resources\\PCLaunch.rc", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\Launch\\Resources\\PCLaunch.rc", "RelativeToolTip": "Launch\\Resources\\PCLaunch.rc", "ViewState": "AgIAAA8AAAAAAAAAAAAAALwAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001005|", "WhenOpened": "2025-08-02T17:13:30.194Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "IntelToolChain.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\ToolChain\\IntelToolChain.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\ToolChain\\IntelToolChain.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\ToolChain\\IntelToolChain.cs", "RelativeToolTip": "UnrealBuildTool\\ToolChain\\IntelToolChain.cs", "ViewState": "AgIAAEQBAAAAAAAAAAAAAFMBAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T17:04:54.394Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "VCToolChain.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\ToolChain\\VCToolChain.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\ToolChain\\VCToolChain.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\ToolChain\\VCToolChain.cs", "RelativeToolTip": "UnrealBuildTool\\ToolChain\\VCToolChain.cs", "ViewState": "AgIAAO8DAAAAAAAAAADwv/gDAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:58:52.955Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "Unity.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\Unity.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\System\\Unity.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\Unity.cs", "RelativeToolTip": "UnrealBuildTool\\System\\Unity.cs", "ViewState": "AgIAAGoAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:58:41.158Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "ActionGraph.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\ActionGraph.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\System\\ActionGraph.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\ActionGraph.cs", "RelativeToolTip": "UnrealBuildTool\\System\\ActionGraph.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:58:32.665Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "BuildException.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\BuildException.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\System\\BuildException.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\BuildException.cs", "RelativeToolTip": "UnrealBuildTool\\System\\BuildException.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:58:30.257Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "UnrealBuildTool.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\UnrealBuildTool.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\System\\UnrealBuildTool.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\UnrealBuildTool.cs", "RelativeToolTip": "UnrealBuildTool\\System\\UnrealBuildTool.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:58:22.721Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "Utils.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\Utils.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\System\\Utils.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\Utils.cs", "RelativeToolTip": "UnrealBuildTool\\System\\Utils.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:58:19.586Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "VCProject.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\VCProject.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\System\\VCProject.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\System\\VCProject.cs", "RelativeToolTip": "UnrealBuildTool\\System\\VCProject.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:58:16.169Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "BuildConfiguration.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\BuildConfiguration.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\Configuration\\BuildConfiguration.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\BuildConfiguration.cs", "RelativeToolTip": "UnrealBuildTool\\Configuration\\BuildConfiguration.cs", "ViewState": "AgIAAMoAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:58:07.435Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UE3BuildMU2Game.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\UE3BuildMU2Game.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\Configuration\\UE3BuildMU2Game.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\UE3BuildMU2Game.cs", "RelativeToolTip": "UnrealBuildTool\\Configuration\\UE3BuildMU2Game.cs", "ViewState": "AgIAAJIAAAAAAAAAAADwv6IAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:50:54.315Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "AndroidToolChain.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\Android\\UnrealBuildTool\\System\\AndroidToolChain.cs", "RelativeDocumentMoniker": "Android\\UnrealBuildTool\\System\\AndroidToolChain.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\Android\\UnrealBuildTool\\System\\AndroidToolChain.cs", "RelativeToolTip": "Android\\UnrealBuildTool\\System\\AndroidToolChain.cs", "ViewState": "AgIAAA4AAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:58:46.268Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "XcodeMacToolChain.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\ToolChain\\XcodeMacToolChain.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\ToolChain\\XcodeMacToolChain.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\ToolChain\\XcodeMacToolChain.cs", "RelativeToolTip": "UnrealBuildTool\\ToolChain\\XcodeMacToolChain.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:58:50.323Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "DebugInfoHeuristic.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\DebugInfoHeuristic.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\Configuration\\DebugInfoHeuristic.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\DebugInfoHeuristic.cs", "RelativeToolTip": "UnrealBuildTool\\Configuration\\DebugInfoHeuristic.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:58:04.438Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "UE3BuildExampleGame.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\UE3BuildExampleGame.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\Configuration\\UE3BuildExampleGame.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\UE3BuildExampleGame.cs", "RelativeToolTip": "UnrealBuildTool\\Configuration\\UE3BuildExampleGame.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:57:58.848Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "UE3BuildWin32.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\UE3BuildWin32.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\Configuration\\UE3BuildWin32.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\UE3BuildWin32.cs", "RelativeToolTip": "UnrealBuildTool\\Configuration\\UE3BuildWin32.cs", "ViewState": "AgIAAHoFAAAAAAAAAADwvyEFAABXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:53:57.93Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "UE3BuildTarget.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\UE3BuildTarget.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\Configuration\\UE3BuildTarget.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\UE3BuildTarget.cs", "RelativeToolTip": "UnrealBuildTool\\Configuration\\UE3BuildTarget.cs", "ViewState": "AgIAALoCAAAAAAAAAADwv8oCAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:53:12.368Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "UE3BuildConfiguration.cs", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\UE3BuildConfiguration.cs", "RelativeDocumentMoniker": "UnrealBuildTool\\Configuration\\UE3BuildConfiguration.cs", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool\\Configuration\\UE3BuildConfiguration.cs", "RelativeToolTip": "UnrealBuildTool\\Configuration\\UE3BuildConfiguration.cs", "ViewState": "AgIAALcAAAAAAAAAAAAAAMMAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T16:50:13.263Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "OnlineAsyncTaskManagerSteam.h", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\OnlineSubsystemSteamworks\\Inc\\OnlineAsyncTaskManagerSteam.h", "RelativeDocumentMoniker": "OnlineSubsystemSteamworks\\Inc\\OnlineAsyncTaskManagerSteam.h", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\OnlineSubsystemSteamworks\\Inc\\OnlineAsyncTaskManagerSteam.h", "RelativeToolTip": "OnlineSubsystemSteamworks\\Inc\\OnlineAsyncTaskManagerSteam.h", "ViewState": "AgIAAAEEAAAAAAAAAAAAAJQEAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-02T13:33:12.287Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "MU2CompactShaderCache.h", "DocumentMoniker": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\MU2Editor\\Inc\\MU2CompactShaderCache.h", "RelativeDocumentMoniker": "MU2Editor\\Inc\\MU2CompactShaderCache.h", "ToolTip": "F:\\Release_Branch\\Client\\MU2\\Development\\Src\\MU2Editor\\Inc\\MU2CompactShaderCache.h", "RelativeToolTip": "MU2Editor\\Inc\\MU2CompactShaderCache.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-02T07:43:31.77Z"}]}]}]}