C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\../../VC\Tools/MSVC\14.44.35207\bin\Hostx64/x86/link.exe
 /MANIFEST /MANIFESTUAC:"level='requireAdministrator' uiAccess='false'" /NOLOGO /DEBUG /errorReport:prompt /MACHINE:x86 /SUBSYSTEM:WINDOWS",5.01" /FIXED:No /LARGEADDRESSAWARE /NXCOMPAT /STACK:2000000,2000000 /SAFESEH /DEF:UnrealEngine3.def /DELAY:UNLOAD /OPT:NOREF /OPT:NOICF /INCREMENTAL:NO /DELAYLOAD:"PhysXLoader.dll" /DELAYLOAD:"d3d11.dll" /DELAYLOAD:"dxgi.dll" /LIBPATH:"../External/gwnav/lib/win32_vc_15.release" /LIBPATH:"../External/gwnav/3rd/tbb41_20121003oss/lib/ia32/vc10" /LIBPATH:"../External/ChromiumEmbeddedFramework/Release" /LIBPATH:"../External/Recast/UE3/lib" /LIBPATH:"../External/Steamworks/sdk/redistributable_bin" /LIBPATH:"../External/DirectShow/Lib" /LIBPATH:"../External/lzopro/lib" /LIBPATH:"../External/zlib/Lib" /LIBPATH:"../External/wxWidgets/lib/vc_dll/win32" /LIBPATH:"../External/FBX/2017.0.1/lib/vs2015/x86/release" /LIBPATH:"../External/EasyHook/" /LIBPATH:"$(DXSDK_DIR)/Lib/x86" /LIBPATH:"../External/nvTextureTools-2.0.6/lib" /LIBPATH:"../External/nvTriStrip/Lib" /LIBPATH:"../External/kiss_fft129/lib/Win32/Release" /LIBPATH:"../External/libvorbis-1.3.2/win32/VS2015/libvorbis/Win32/Release" /LIBPATH:"../External/libvorbis-1.3.2/win32/VS2015/libvorbisfile/Win32/Release" /LIBPATH:"../External/libogg-1.2.2/win32/VS2015/Win32/Release" /LIBPATH:"../External/GFx/Lib/Win32/Msvc15/Release" /LIBPATH:"../External/GFx/3rdParty/jpeg-8d/lib/Win32/Msvc15/Release" /LIBPATH:"../External/GFx/3rdParty/pcre/Lib/Win32/Msvc15/Release" /LIBPATH:"../External/GFx/3rdParty/fmod/pc/Win32/lib" /LIBPATH:"../External/AgPerfMon/lib/win32" /LIBPATH:"../External/GamersSDK/4.2.1/lib/Win32/" /LIBPATH:"../External/DECtalk464/lib/Win32" /LIBPATH:"../External/FaceFX/FxSDK/lib/win32/vs10/" /LIBPATH:"../External/FaceFX/FxCG/lib/win32/vs10/" /LIBPATH:"../External/FaceFX/FxAnalysis/lib/win32/vs10/" /LIBPATH:"../External/FaceFX/Studio/External/libresample-0.1.3/lib/win32/vs10/" /LIBPATH:"../External/PhysX/SDKs/lib/win32/" /LIBPATH:"../External/PhysX/Nxd/lib/win32/" /LIBPATH:"../External/PhysX/SDKs/TetraMaker/lib/win32/" /LIBPATH:"../External/PhysX/APEX/lib/vc10win32-PhysX_2.8.4/" /LIBPATH:"../External/libPNG/lib/" /LIBPATH:"../External/nvDXT/Lib/" /LIBPATH:"../External/ConvexDecomposition/Lib/" /LIBPATH:"../External/TestTrack/Lib/Release/" /LIBPATH:"../External/nvapi/" /LIBPATH:"../External/nvtesslib/lib/Win32/" /LIBPATH:"../../../../Vendor/boost/stage/lib/x86/" /LIBPATH:"../External/GameGuard/x86/" /NODEFAULTLIB:"MSVCRTD" /NODEFAULTLIB:"MSVCPRTD" /NODEFAULTLIB:"LIBC" /NODEFAULTLIB:"LIBCMT" /NODEFAULTLIB:"LIBCPMT" /NODEFAULTLIB:"LIBCP" /NODEFAULTLIB:"LIBCD" /NODEFAULTLIB:"LIBCMTD" /NODEFAULTLIB:"LIBCPMTD" /NODEFAULTLIB:"LIBCPD" @"F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.exe.response" /OUT:"F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.exe" /IMPLIB:"F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.lib" /PDB:"F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.pdb" /CLRTHREADATTRIBUTE:STA