﻿#pragma checksum "ContentBrowser\AssetInspector.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E890B00E808F941A34C63D8A73079EC43EBCBB27"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using ContentBrowser;
using CustomControls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ContentBrowser {
    
    
    /// <summary>
    /// AssetInspector
    /// </summary>
    public partial class AssetInspector : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 4 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.AssetInspector This;
        
        #line default
        #line hidden
        
        
        #line 36 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock mNoTagsLabel;
        
        #line default
        #line hidden
        
        
        #line 38 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel mTagsWrapPanel;
        
        #line default
        #line hidden
        
        
        #line 49 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.YesNoPrompt mProceedWithTaggingPrompt;
        
        #line default
        #line hidden
        
        
        #line 54 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.YesNoPrompt mProceedWithUntaggingPrompt;
        
        #line default
        #line hidden
        
        
        #line 68 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button mDestroyTagModeButton;
        
        #line default
        #line hidden
        
        
        #line 72 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button mCreateTagButton;
        
        #line default
        #line hidden
        
        
        #line 73 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.NameEntryPrompt mCreateTagPrompt;
        
        #line default
        #line hidden
        
        
        #line 76 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.YesNoPrompt mDestroyTagPrompt;
        
        #line default
        #line hidden
        
        
        #line 82 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SlateBorder mTagsPaletteBorder;
        
        #line default
        #line hidden
        
        
        #line 99 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.UnrealTextBox mTagsPaletteFilter;
        
        #line default
        #line hidden
        
        
        #line 120 "ContentBrowser\AssetInspector.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox mTagsPalette;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UnrealEdCSharp;component/contentbrowser/assetinspector.xaml", System.UriKind.Relative);
            
            #line 1 "ContentBrowser\AssetInspector.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.This = ((ContentBrowser.AssetInspector)(target));
            return;
            case 2:
            this.mNoTagsLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.mTagsWrapPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 4:
            this.mProceedWithTaggingPrompt = ((CustomControls.YesNoPrompt)(target));
            return;
            case 5:
            this.mProceedWithUntaggingPrompt = ((CustomControls.YesNoPrompt)(target));
            return;
            case 6:
            this.mDestroyTagModeButton = ((System.Windows.Controls.Button)(target));
            return;
            case 7:
            this.mCreateTagButton = ((System.Windows.Controls.Button)(target));
            return;
            case 8:
            this.mCreateTagPrompt = ((CustomControls.NameEntryPrompt)(target));
            return;
            case 9:
            this.mDestroyTagPrompt = ((CustomControls.YesNoPrompt)(target));
            return;
            case 10:
            this.mTagsPaletteBorder = ((CustomControls.SlateBorder)(target));
            return;
            case 11:
            this.mTagsPaletteFilter = ((CustomControls.UnrealTextBox)(target));
            return;
            case 12:
            this.mTagsPalette = ((System.Windows.Controls.ListBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

