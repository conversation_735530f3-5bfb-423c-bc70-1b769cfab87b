﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseWithGuardIT|Win32">
      <Configuration>ReleaseWithGuardIT</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseWithGuardIT|x64">
      <Configuration>ReleaseWithGuardIT</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ShippingWithGuardIT|Win32">
      <Configuration>ShippingWithGuardIT</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ShippingWithGuardIT|x64">
      <Configuration>ShippingWithGuardIT</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|Win32">
      <Configuration>Shipping</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|x64">
      <Configuration>Shipping</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5F2701A0-A1B7-4C4A-9DCF-3038CCA2EC6A}</ProjectGuid>
    <RootNamespace>MU2Game Win32</RootNamespace>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccProvider>
    </SccProvider>
    <Keyword>MakeFileProj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.40219.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">..\..\Intermediate\Unused\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">..\..\Intermediate\Unused\</IntDir>
    <NMakeBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">@cd ..
@call Targets/Build.bat MU2Game Win32 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win32/MU2Game-Win32-Debug.exe"</NMakeBuildCommandLine>
    <NMakeBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">@cd ..
@call Targets/Build.bat MU2Game Win64 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win64/MU2Game-Win64-Debug.exe"</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">@cd ..
@call Targets/CleanGame.bat MU2Game Win32 $(Configuration) "../../Binaries/Win32/MU2Game-Win32-Debug.exe"
@call Targets/Build.bat MU2Game Win32 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win32/MU2Game-Win32-Debug.exe"</NMakeReBuildCommandLine>
    <NMakeReBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">@cd ..
@call Targets/CleanGame.bat MU2Game Win64 $(Configuration) "../../Binaries/Win64/MU2Game-Win64-Debug.exe"
@call Targets/Build.bat MU2Game Win64 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win32/MU2Game-Win64-Debug.exe"</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">@cd ..
@call Targets/CleanGame.bat MU2Game Win32 $(Configuration) "../../Binaries/Win32/MU2Game-Win32-Debug.exe"</NMakeCleanCommandLine>
    <NMakeCleanCommandLine Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">@cd ..
@call Targets/CleanGame.bat MU2Game Win64 $(Configuration) "../../Binaries/Win64/MU2Game-Win64-Debug.exe"</NMakeCleanCommandLine>
    <NMakeOutput Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">../../../Binaries/Win32/MU2Game-Win32-Debug.exe</NMakeOutput>
    <NMakeOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">../../../Binaries/Win64/MU2Game-Win64-Debug.exe</NMakeOutput>
    <NMakePreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakePreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakeIncludeSearchPath Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <NMakeIncludeSearchPath Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <NMakeForcedIncludes Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeForcedIncludes Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeAssemblySearchPath Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <NMakeAssemblySearchPath Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <NMakeForcedUsingAssemblies Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(NMakeForcedUsingAssemblies)</NMakeForcedUsingAssemblies>
    <NMakeForcedUsingAssemblies Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(NMakeForcedUsingAssemblies)</NMakeForcedUsingAssemblies>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">..\..\Intermediate\Unused\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">..\..\Intermediate\Unused\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">..\..\Intermediate\Unused\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">..\..\Intermediate\Unused\</IntDir>
    <NMakeBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">@cd ..
@call Targets/Build.bat MU2Game Win32 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win32/MU2Game.exe"</NMakeBuildCommandLine>
    <NMakeBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">@cd ..
del ..\..\Binaries\Win32\MU2Game.exe
@call Targets/Build.bat MU2Game Win32 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -DEFINE WITH_GUARDIT=1 -output "../../Binaries/Win32/MU2Game.exe"
@call Targets/GuardIt.bat MU2Game
</NMakeBuildCommandLine>
    <NMakeBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Release|x64'">@cd ..
@call Targets/Build.bat MU2Game Win64 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win64/MU2Game.exe"</NMakeBuildCommandLine>
    <NMakeBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">@cd ..
@call Targets/Build.bat MU2Game Win64 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win64/MU2Game.exe"</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">@cd ..
@call Targets/CleanGame.bat MU2Game Win32 $(Configuration) "../../Binaries/Win32/MU2Game.exe"
@call Targets/Build.bat MU2Game Win32 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win32/MU2Game.exe"</NMakeReBuildCommandLine>
    <NMakeReBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">@cd ..
@call Targets/CleanGame.bat MU2Game Win32 $(Configuration) "../../Binaries/Win32/MU2Game.exe"
@call Targets/Build.bat MU2Game Win32 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -DEFINE WITH_GUARDIT=1 -output "../../Binaries/Win32/MU2Game.exe"
@call Targets/GuardIt.bat MU2Game
</NMakeReBuildCommandLine>
    <NMakeReBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Release|x64'">@cd ..
@call Targets/CleanGame.bat MU2Game Win64 $(Configuration) "../../Binaries/Win64/MU2Game.exe"
@call Targets/Build.bat MU2Game Win64 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win64/MU2Game.exe"</NMakeReBuildCommandLine>
    <NMakeReBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">@cd ..
@call Targets/CleanGame.bat MU2Game Win64 $(Configuration) "../../Binaries/Win64/MU2Game.exe"
@call Targets/Build.bat MU2Game Win64 $(Configuration) -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win64/MU2Game.exe"</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">@cd ..
@call Targets/CleanGame.bat MU2Game Win32 $(Configuration) "../../Binaries/Win32/MU2Game.exe"</NMakeCleanCommandLine>
    <NMakeCleanCommandLine Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">@cd ..
@call Targets/CleanGame.bat MU2Game Win32 $(Configuration) "../../Binaries/Win32/MU2Game.exe"</NMakeCleanCommandLine>
    <NMakeCleanCommandLine Condition="'$(Configuration)|$(Platform)'=='Release|x64'">@cd ..
@call Targets/CleanGame.bat MU2Game Win64 $(Configuration) "../../Binaries/Win64/MU2Game.exe"</NMakeCleanCommandLine>
    <NMakeCleanCommandLine Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">@cd ..
@call Targets/CleanGame.bat MU2Game Win64 $(Configuration) "../../Binaries/Win64/MU2Game.exe"</NMakeCleanCommandLine>
    <NMakeOutput Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">../../../Binaries/Win32/MU2Game.exe</NMakeOutput>
    <NMakeOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">../../../Binaries/Win32/MU2Game.exe</NMakeOutput>
    <NMakeOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">../../../Binaries/Win64/MU2Game.exe</NMakeOutput>
    <NMakeOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">../../../Binaries/Win64/MU2Game.exe</NMakeOutput>
    <NMakePreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakePreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakePreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakePreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakeIncludeSearchPath Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <NMakeIncludeSearchPath Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <NMakeIncludeSearchPath Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <NMakeIncludeSearchPath Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <NMakeForcedIncludes Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeForcedIncludes Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeForcedIncludes Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeForcedIncludes Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeAssemblySearchPath Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <NMakeAssemblySearchPath Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <NMakeAssemblySearchPath Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <NMakeAssemblySearchPath Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <NMakeForcedUsingAssemblies Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(NMakeForcedUsingAssemblies)</NMakeForcedUsingAssemblies>
    <NMakeForcedUsingAssemblies Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">$(NMakeForcedUsingAssemblies)</NMakeForcedUsingAssemblies>
    <NMakeForcedUsingAssemblies Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(NMakeForcedUsingAssemblies)</NMakeForcedUsingAssemblies>
    <NMakeForcedUsingAssemblies Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">$(NMakeForcedUsingAssemblies)</NMakeForcedUsingAssemblies>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">..\..\Intermediate\Unused\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">..\..\Intermediate\Unused\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">..\..\Intermediate\Unused\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">..\..\Intermediate\Unused\</IntDir>
    <NMakeBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">@cd ..
@call Targets/Build.bat MU2Game Win32 $(Configuration) -vs2015 -NOEDITOR -DEFINE WITH_LEAN_AND_MEAN_UE3=1 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win32/MULegend.exe"</NMakeBuildCommandLine>
    <NMakeBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">@cd ..
@call Targets/Build.bat MU2Game Win32 $(Configuration) -vs2015 -NOEDITOR -DEFINE WITH_LEAN_AND_MEAN_UE3=1 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -DEFINE WITH_GUARDIT=1 -output "../../Binaries/Win32/MULegend.exe"</NMakeBuildCommandLine>
    <NMakeBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">@cd ..
@call Targets/Build.bat MU2Game Win64 $(Configuration) -vs2015 -NOEDITOR -DEFINE WITH_LEAN_AND_MEAN_UE3=1 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win64/MULegend.exe"</NMakeBuildCommandLine>
    <NMakeBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'">@cd ..
@call Targets/Build.bat MU2Game Win64 $(Configuration) -vs2015 -NOEDITOR -DEFINE WITH_LEAN_AND_MEAN_UE3=1 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -DEFINE WITH_GUARDIT=1 -output "../../Binaries/Win64/MULegend.exe"</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">@cd ..
@call Targets/CleanGame.bat MU2Game Win32 $(Configuration) ../../Binaries/Win32/MULegend.exe"
@call Targets/Build.bat MU2Game Win32 $(Configuration) -vs2015 -NOEDITOR -DEFINE WITH_LEAN_AND_MEAN_UE3=1 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win32/MULegend.exe"</NMakeReBuildCommandLine>
    <NMakeReBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">@cd ..
@call Targets/CleanGame.bat MU2Game Win32 $(Configuration) ../../Binaries/Win32/MULegend.exe"
@call Targets/Build.bat MU2Game Win32 $(Configuration) -vs2015 -NOEDITOR -DEFINE WITH_LEAN_AND_MEAN_UE3=1 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -DEFINE WITH_GUARDIT=1 -output "../../Binaries/Win32/MULegend.exe"</NMakeReBuildCommandLine>
    <NMakeReBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">@cd ..
@call Targets/CleanGame.bat MU2Game Win64 $(Configuration) ../../Binaries/Win64/MULegend.exe"
@call Targets/Build.bat MU2Game Win64 $(Configuration) -vs2015 -NOEDITOR -DEFINE WITH_LEAN_AND_MEAN_UE3=1 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win64/MULegend.exe"</NMakeReBuildCommandLine>
    <NMakeReBuildCommandLine Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'">@cd ..
@call Targets/CleanGame.bat MU2Game Win64 $(Configuration) ../../Binaries/Win64/MULegend.exe"
@call Targets/Build.bat MU2Game Win64 $(Configuration) -vs2015 -NOEDITOR -DEFINE WITH_LEAN_AND_MEAN_UE3=1 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -DEFINE WITH_GUARDIT=1 -output "../../Binaries/Win64/MULegend.exe"</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">@cd ..
@call Targets/CleanGame.bat MU2Game Win32 $(Configuration) ../../Binaries/Win32/MULegend.exe"</NMakeCleanCommandLine>
    <NMakeCleanCommandLine Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">@cd ..
@call Targets/CleanGame.bat MU2Game Win32 $(Configuration) ../../Binaries/Win32/MULegend.exe"</NMakeCleanCommandLine>
    <NMakeCleanCommandLine Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">@cd ..
@call Targets/CleanGame.bat MU2Game Win64 $(Configuration) ../../Binaries/Win64/MULegend.exe"</NMakeCleanCommandLine>
    <NMakeCleanCommandLine Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'">@cd ..
@call Targets/CleanGame.bat MU2Game Win64 $(Configuration) ../../Binaries/Win64/MULegend.exe"</NMakeCleanCommandLine>
    <NMakeOutput Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">../../../Binaries/Win32/MULegend.exe</NMakeOutput>
    <NMakeOutput Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">../../../Binaries/Win32/MULegend.exe</NMakeOutput>
    <NMakeOutput Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">../../../Binaries/Win64/MULegend.exe</NMakeOutput>
    <NMakeOutput Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'">../../../Binaries/Win64/MULegend.exe</NMakeOutput>
    <NMakePreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakePreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakePreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakePreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'">$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakeIncludeSearchPath Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <NMakeIncludeSearchPath Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <NMakeIncludeSearchPath Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <NMakeIncludeSearchPath Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'">$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <NMakeForcedIncludes Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeForcedIncludes Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeForcedIncludes Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeForcedIncludes Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'">$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeAssemblySearchPath Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <NMakeAssemblySearchPath Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <NMakeAssemblySearchPath Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <NMakeAssemblySearchPath Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'">$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <NMakeForcedUsingAssemblies Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">$(NMakeForcedUsingAssemblies)</NMakeForcedUsingAssemblies>
    <NMakeForcedUsingAssemblies Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">$(NMakeForcedUsingAssemblies)</NMakeForcedUsingAssemblies>
    <NMakeForcedUsingAssemblies Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">$(NMakeForcedUsingAssemblies)</NMakeForcedUsingAssemblies>
    <NMakeForcedUsingAssemblies Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'">$(NMakeForcedUsingAssemblies)</NMakeForcedUsingAssemblies>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IncludePath)</IncludePath>
    <LibraryPath Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(LibraryPath)</LibraryPath>
    <LibraryPath Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(LibraryPath)</LibraryPath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">$(IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">$(IncludePath)</IncludePath>
    <LibraryPath Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <LibraryPath Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(NETFXKitsDir)Lib\um\x86</LibraryPath>
    <LibraryPath Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(LibraryPath)</LibraryPath>
    <LibraryPath Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ExecutablePath>$(VC_ExecutablePath_x86);$(WindowsSDK_ExecutablePath);$(VS_ExecutablePath);$(MSBuild_ExecutablePath);$(SystemRoot)\SysWow64;$(FxCopDir);$(PATH);</ExecutablePath>
    <LibraryWPath>$(WindowsSDK_MetadataPath);</LibraryWPath>
    <ExcludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(MSBuild_ExecutablePath);$(VC_LibraryPath_x86);</ExcludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">
    <ExecutablePath>$(VC_ExecutablePath_x86);$(WindowsSDK_ExecutablePath);$(VS_ExecutablePath);$(MSBuild_ExecutablePath);$(SystemRoot)\SysWow64;$(FxCopDir);$(PATH);</ExecutablePath>
    <LibraryWPath>$(WindowsSDK_MetadataPath);</LibraryWPath>
    <ExcludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(MSBuild_ExecutablePath);$(VC_LibraryPath_x86);</ExcludePath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <BuildLog>
      <Path>..\..\Intermediate\BuildLogs\$(ProjectName) $(Configuration)\BuildLog.htm</Path>
    </BuildLog>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <BuildLog>
      <Path>..\..\Intermediate\BuildLogs\$(ProjectName) $(Configuration)\BuildLog.htm</Path>
    </BuildLog>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <BuildLog>
      <Path>..\..\Intermediate\BuildLogs\$(ProjectName) $(Configuration)\BuildLog.htm</Path>
    </BuildLog>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|Win32'">
    <BuildLog>
      <Path>..\..\Intermediate\BuildLogs\$(ProjectName) $(Configuration)\BuildLog.htm</Path>
    </BuildLog>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <BuildLog>
      <Path>..\..\Intermediate\BuildLogs\$(ProjectName) $(Configuration)\BuildLog.htm</Path>
    </BuildLog>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseWithGuardIT|x64'">
    <BuildLog>
      <Path>..\..\Intermediate\BuildLogs\$(ProjectName) $(Configuration)\BuildLog.htm</Path>
    </BuildLog>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">
    <BuildLog>
      <Path>..\..\Intermediate\BuildLogs\$(ProjectName) $(Configuration)\BuildLog.htm</Path>
    </BuildLog>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|Win32'">
    <BuildLog>
      <Path>..\..\Intermediate\BuildLogs\$(ProjectName) $(Configuration)\BuildLog.htm</Path>
    </BuildLog>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">
    <BuildLog>
      <Path>..\..\Intermediate\BuildLogs\$(ProjectName) $(Configuration)\BuildLog.htm</Path>
    </BuildLog>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ShippingWithGuardIT|x64'">
    <BuildLog>
      <Path>..\..\Intermediate\BuildLogs\$(ProjectName) $(Configuration)\BuildLog.htm</Path>
    </BuildLog>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ProjectReference Include="..\UnrealBuildTool\UnrealBuildTool.csproj">
      <Project>{fd7c5e1a-cfe4-4fd5-a525-1eb1599a39ac}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>