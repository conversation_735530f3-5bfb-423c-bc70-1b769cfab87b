// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"
#include "../../../../Shared/Version.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

#include "messages.h"
#include "messages.rc"


/////////////////////////////////////////////////////////////////////////////
//
// Game names
//
// These are just ids given to the different game names so they can be compared against the external GAMENAME definition.
// They don't need to match the equivalent game names in LaunchPrivate.h.
#define EXAMPLEGAME 0
#define UTGAME 1
#define GEARGAME 2
#define EXOGAME 3
#define FORTRESSGAME 4
#define SWORDGAME 5
// WJC_MU2
#define MU2GAME	8
//~WJC_MU2


/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_YESNO2ALL DIALOGEX 0, 0, 275, 118
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "Yes",IDC_YES,25,97,50,14
    PUSHBUTTON      "No",IDC_NO_B,141,97,50,14
    PUSHBUTTON      "Yes to all",IDC_YESTOALL,83,97,50,14
    PUSHBUTTON      "No to all",IDC_NOTOALL,199,97,50,14
    LTEXT           "Static",IDC_MESSAGE,17,19,246,66
END

IDD_YESNO2ALLCANCEL DIALOGEX 0, 0, 297, 126
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CENTER | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    PUSHBUTTON      "Yes",IDC_YES,15,98,50,14
    PUSHBUTTON      "Yes to all",IDC_YESTOALL,70,98,50,14
    PUSHBUTTON      "No",IDC_NO_B,124,98,50,14
    PUSHBUTTON      "No to all",IDC_NOTOALL,177,98,50,14
    PUSHBUTTON      "Cancel",IDC_CANCEL,232,98,50,14
    LTEXT           "Static",IDC_MESSAGE,16,14,265,66
END

/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO 
BEGIN
    IDD_YESNO2ALL, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 268
        TOPMARGIN, 7
        BOTTOMMARGIN, 111
    END

	IDD_YESNO2ALLCANCEL, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 290
        TOPMARGIN, 7
        BOTTOMMARGIN, 119
    END
END
#endif    // APSTUDIO_INVOKED

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#include ""afxres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED

#if 1

#if GAMENAME==GEARGAME
#if DEDICATED_SERVER
	#define PRODUCTNAME		"Gears of War 3 Dedicated Server"
	#define FILEDESC		"Gears of War 3 Dedicated Server"
	#define INTERNALNAME	"GearGameServer"
	#define ORIGINALNAME	"GearGameServer.exe"
#else
	#define PRODUCTNAME		"Gears of War 3"
	#define FILEDESC		"Gears of War 3"
	#define INTERNALNAME	"GearGame"
	#define ORIGINALNAME	"GearGame.exe"
#endif // DEDICATED_SERVER

#elif GAMENAME==FORTRESSGAME
#if DEDICATED_SERVER
	#define PRODUCTNAME	"Fortress Dedicated Server"
	#define FILEDESC		"Fortress Dedicated Server"
	#define INTERNALNAME	"FortressGameServer"
	#define ORIGINALNAME	"FortressGameServer.exe"
#else
	#define PRODUCTNAME	"Fortress"
	#define FILEDESC		"Fortress"
	#define INTERNALNAME	"FortressGame"
	#define ORIGINALNAME	"FortressGame.exe"
#endif // DEDICATED_SERVER 

#elif UDK==1
	#define PRODUCTNAME	"Unreal Development Kit"
	#define FILEDESC		"BUILT WITH UDK"
	#define INTERNALNAME	"UDK"
	#define ORIGINALNAME	"UDK.exe"

// WJC_MU2
#elif GAMENAME==MU2GAME
#if DEDICATED_SERVER
	#define PRODUCTNAME		"MULegend Dedicated Server"
	#define FILEDESC		"MULegend Dedicated Server"
	#define INTERNALNAME	"MULegendServer"
	#define ORIGINALNAME	"MULegendServer.exe"
#else
	#define PRODUCTNAME		"MULegend"
	#define FILEDESC		"MULegend"
	#define INTERNALNAME	"MULegend"
	#define ORIGINALNAME	"MULegend.exe"
#endif // DEDICATED_SERVER 
//~WJC_MU2

#else
	#define PRODUCTNAME		""
	#define FILEDESC		""
	#define INTERNALNAME	""
	#define ORIGINALNAME	""
#endif

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION MU2_MAJOR_VERSION,MU2_MINOR_VERSION,MU2_PATCH_VERSION,0
 PRODUCTVERSION MU2_MAJOR_VERSION,MU2_MINOR_VERSION,MU2_PATCH_VERSION,0
 FILEFLAGSMASK 0x17L
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
		// WJC_MU2
            VALUE "CompanyName", "Webzen"
            VALUE "LegalCopyright", "Copyright 2013 Webzen. All Rights Reserved."
		// ~WJC_MU2
            VALUE "ProductName", PRODUCTNAME
			VALUE "FileDescription", FILEDESC
            VALUE "InternalName", INTERNALNAME
            VALUE "OriginalFilename", ORIGINALNAME
			VALUE "FileVersion", "1, 0, 8767, 0"
			VALUE "ProductVersion", "1, 0, 8767, 0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

#endif // GAMENAME==UTGAME

/////////////////////////////////////////////////////////////////////////////
//
// Application Manifest
//

#if _DEBUG

// Debug manifest
#if _WIN64
1						RT_MANIFEST				"Debug-PCLaunch-Win64.manifest"
#else
1						RT_MANIFEST				"Debug-PCLaunch-Win32.manifest"
#endif

#else

// Release manifest
#if _WIN64
1						RT_MANIFEST				"PCLaunch-Win64.manifest"
#else
1						RT_MANIFEST				"PCLaunch-Win32.manifest"
#endif

#endif

/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
//IDICON_Game             ICON                    "Unreal.ico"

#if UDK
IDICON_UDKGame          ICON                    "UDK.ico"
IDICON_UDKEditor        ICON                    "UDK.ico"
#elif GAMENAME==GEARGAME
IDICON_GoW              ICON                    "GearsOfWar.ico"
IDICON_GoWEditor        ICON                    "GearsEditor.ico"
#elif GAMENAME==FORTRESSGAME
IDICON_FortressGame       ICON               "FortressGame.ico"
IDICON_FortressGameEditor ICON               "FortressGameEditor.ico"
#elif GAMENAME==UTGAME
IDICON_UTGame           ICON                    "Envy.ico"
IDICON_UTEditor         ICON                    "EnvyEditor.ico"
IDICON_UDKGame          ICON                    "UDK.ico"
IDICON_UDKEditor        ICON                    "UDK.ico"
#elif GAMENAME==EXAMPLEGAME
IDICON_DemoGame         ICON                    "Demogame.ico"
IDICON_DemoEditor       ICON                    "DemoEditor.ico"
#elif GAMENAME==EXOGAME
IDICON_DemoGame         ICON                    "Demogame.ico"
IDICON_DemoEditor       ICON                    "DemoEditor.ico"
#elif GAMENAME==SWORDGAME
IDICON_DemoGame         ICON                    "Demogame.ico"
IDICON_DemoEditor       ICON                    "DemoEditor.ico"
// WJC_MU2
#elif GAMENAME==MU2GAME
IDICON_MU2Game          ICON                    "MU2Game.ico"
IDICON_MU2Editor        ICON                    "MU2Editor.ico"
//~WJC_MU2
#else
	#error Hook up your game name here
#endif

/////////////////////////////////////////////////////////////////////////////
//
// Bitmap
//

//WXBITMAP_STD_COLOURS    BITMAP                  "colours.bmp"


/////////////////////////////////////////////////////////////////////////////
//
// Cursor
//

IDCUR_NODROP            CURSOR                  "nodrop.cur"
IDCUR_MOVE              CURSOR                  "arrow.cur"
IDCUR_COPY              CURSOR                  "arrowcop.cur"
IDCUR_GRABHAND          CURSOR                  "grabhand.cur"
WXCURSOR_CROSS          CURSOR                  "crosshai.cur"

#if UDK
IDCUR_CUSTOM1			CURSOR					"arrow.cur"
IDCUR_CUSTOM2			CURSOR					"arrow.cur"
IDCUR_CUSTOM3			CURSOR                  "arrow.cur"
IDCUR_CUSTOM4			CURSOR                  "arrow.cur"
IDCUR_CUSTOM5			CURSOR                  "arrow.cur"
IDCUR_CUSTOM6			CURSOR                  "arrow.cur"
IDCUR_CUSTOM7			CURSOR                  "arrow.cur"
IDCUR_CUSTOM8			CURSOR                  "arrow.cur"
IDCUR_CUSTOM9			CURSOR                  "arrow.cur"
#elif GAMENAME==GEARGAME
IDCUR_CUSTOM1			CURSOR                  "arrow.cur"
IDCUR_CUSTOM2			CURSOR                  "arrow.cur"
IDCUR_CUSTOM3			CURSOR                  "arrow.cur"
IDCUR_CUSTOM4			CURSOR                  "arrow.cur"
IDCUR_CUSTOM5			CURSOR                  "arrow.cur"
IDCUR_CUSTOM6			CURSOR                  "arrow.cur"
IDCUR_CUSTOM7			CURSOR                  "arrow.cur"
IDCUR_CUSTOM8			CURSOR                  "arrow.cur"
IDCUR_CUSTOM9			CURSOR                  "arrow.cur"
#elif GAMENAME==FORTRESSGAME
IDCUR_CUSTOM1			CURSOR					"fortnite_arrow.cur"
IDCUR_CUSTOM2			CURSOR                  "arrow.cur"
IDCUR_CUSTOM3			CURSOR                  "arrow.cur"
IDCUR_CUSTOM4			CURSOR                  "arrow.cur"
IDCUR_CUSTOM5			CURSOR                  "arrow.cur"
IDCUR_CUSTOM6			CURSOR                  "arrow.cur"
IDCUR_CUSTOM7			CURSOR                  "arrow.cur"
IDCUR_CUSTOM8			CURSOR                  "arrow.cur"
IDCUR_CUSTOM9			CURSOR                  "arrow.cur"
#elif GAMENAME==UTGAME
IDCUR_CUSTOM1			CURSOR                  "arrow.cur"
IDCUR_CUSTOM2			CURSOR                  "arrow.cur"
IDCUR_CUSTOM3			CURSOR                  "arrow.cur"
IDCUR_CUSTOM4			CURSOR                  "arrow.cur"
IDCUR_CUSTOM5			CURSOR                  "arrow.cur"
IDCUR_CUSTOM6			CURSOR                  "arrow.cur"
IDCUR_CUSTOM7			CURSOR                  "arrow.cur"
IDCUR_CUSTOM8			CURSOR                  "arrow.cur"
IDCUR_CUSTOM9			CURSOR                  "arrow.cur"
#elif GAMENAME==EXAMPLEGAME
IDCUR_CUSTOM1			CURSOR                  "arrow.cur"
IDCUR_CUSTOM2			CURSOR                  "arrow.cur"
IDCUR_CUSTOM3			CURSOR                  "arrow.cur"
IDCUR_CUSTOM4			CURSOR                  "arrow.cur"
IDCUR_CUSTOM5			CURSOR                  "arrow.cur"
IDCUR_CUSTOM6			CURSOR                  "arrow.cur"
IDCUR_CUSTOM7			CURSOR                  "arrow.cur"
IDCUR_CUSTOM8			CURSOR                  "arrow.cur"
IDCUR_CUSTOM9			CURSOR                  "arrow.cur"
#elif GAMENAME==EXOGAME
IDCUR_CUSTOM1			CURSOR                  "arrow.cur"
IDCUR_CUSTOM2			CURSOR                  "arrow.cur"
IDCUR_CUSTOM3			CURSOR                  "arrow.cur"
IDCUR_CUSTOM4			CURSOR                  "arrow.cur"
IDCUR_CUSTOM5			CURSOR                  "arrow.cur"
IDCUR_CUSTOM6			CURSOR                  "arrow.cur"
IDCUR_CUSTOM7			CURSOR                  "arrow.cur"
IDCUR_CUSTOM8			CURSOR                  "arrow.cur"
IDCUR_CUSTOM9			CURSOR                  "arrow.cur"
#elif GAMENAME==SWORDGAME
IDCUR_CUSTOM1			CURSOR                  "arrow.cur"
IDCUR_CUSTOM2			CURSOR                  "arrow.cur"
IDCUR_CUSTOM3			CURSOR                  "arrow.cur"
IDCUR_CUSTOM4			CURSOR                  "arrow.cur"
IDCUR_CUSTOM5			CURSOR                  "arrow.cur"
IDCUR_CUSTOM6			CURSOR                  "arrow.cur"
IDCUR_CUSTOM7			CURSOR                  "arrow.cur"
IDCUR_CUSTOM8			CURSOR                  "arrow.cur"
IDCUR_CUSTOM9			CURSOR                  "arrow.cur"
// WJC_MU2
#elif GAMENAME==MU2GAME
IDCUR_CUSTOM1			CURSOR                  "MU2_Normal.cur"
IDCUR_CUSTOM2			CURSOR                  "MU2_Pickup.cur"
IDCUR_CUSTOM3			CURSOR                  "MU2_Attack.cur"
IDCUR_CUSTOM4			CURSOR                  "MU2_Event.cur"
IDCUR_CUSTOM5			CURSOR                  "MU2_SkillMove.cur"
IDCUR_CUSTOM6			CURSOR                  "MU2_Block.cur"
IDCUR_CUSTOM7			CURSOR                  "MU2_Repair.cur"
IDCUR_CUSTOM8			CURSOR                  "MU2_Delete.cur"
IDCUR_CUSTOM9			CURSOR                  "MU2_Enchant.cur"
IDCUR_CUSTOM10			CURSOR                  "MU2_Collect.cur"
IDCUR_CUSTOM11			CURSOR                  "MU2_EnablePortal.cur"
IDCUR_CUSTOM12			CURSOR                  "MU2_DisablePortal.cur"
IDCUR_CUSTOM13			CURSOR					"MU2_SocketEquip.cur"
IDCUR_CUSTOM14			CURSOR					"MU2_Decompose.cur"
IDCUR_CUSTOM15			CURSOR					"MU2_AltMode.cur"
IDCUR_CUSTOM16			CURSOR					"MU2_AlternativeCusor.cur"
// 注释掉缺失的资源文件
// IDCUR_CUSTOM17			CURSOR					"MU2_AlternativeCusor17.cur"
// IDCUR_CUSTOM18			CURSOR					"MU2_AlternativeCusor18.cur"
// IDCUR_CUSTOM19			CURSOR					"MU2_AlternativeCusor19.cur"
// IDCUR_CUSTOM20			CURSOR					"MU2_AlternativeCusor20.cur"
// IDCUR_CUSTOM21			CURSOR					"MU2_AlternativeCusor21.cur"
// IDCUR_CUSTOM22			CURSOR					"MU2_AlternativeCusor22.cur"
// IDCUR_CUSTOM23			CURSOR					"MU2_AlternativeCusor23.cur"
// IDCUR_CUSTOM24			CURSOR					"MU2_AlternativeCusor24.cur"

// ~WJC_MU2
#else
	#error Hook up your game name here
#endif


#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////


/**
 * Games for Windows Live SPA file inclusion
 */
#if WITH_PANORAMA
	#if GAMENAME==GEARGAME
		ID_SPAFILE RT_RCDATA "..\\..\\GearGame\\Live\\Rift.spa"
	#elif GAMENAME==UTGAME
		ID_SPAFILE RT_RCDATA "..\\..\\UTGame\\Live\\UT3.spa"
	#elif GAMENAME==EXAMPLEGAME
		ID_SPAFILE RT_RCDATA "..\\..\\ExampleGame\\Live\\ExampleGame.spa"
	#elif GAMENAME==EXOGAME
		ID_SPAFILE RT_RCDATA "..\\..\\ExoGame\\Live\\ExoGame.spa"
	#else
		#error Hook up your game name here
	#endif
#endif

/** 
 * Hash file included as raw resource in exe
 */
#if GAMENAME==GEARGAME
	ID_HASHFILE RCDATA "..\\..\\..\\..\\GearGame\\Build\\Hashes.sha"
#elif GAMENAME==FORTRESSGAME
	ID_HASHFILE RCDATA "..\\..\\..\\..\\FortressGame\\Build\\Hashes.sha"
#elif GAMENAME==UTGAME
	ID_HASHFILE RCDATA "..\\..\\..\\..\\UDKGame\\Build\\Hashes.sha"
#elif GAMENAME==EXAMPLEGAME
	ID_HASHFILE RCDATA "..\\..\\..\\..\\ExampleGame\\Build\\Hashes.sha"
#elif GAMENAME==EXOGAME
	ID_HASHFILE RCDATA "..\\..\\..\\..\\ExoGame\\Build\\Hashes.sha"
#elif GAMENAME==SWORDGAME
	ID_HASHFILE RCDATA "..\\..\\..\\..\\SwordGame\\Build\\Hashes.sha"
		// WJC_MU2
#elif GAMENAME==MU2GAME
	ID_HASHFILE RCDATA "..\\..\\..\\..\\LegendGame\\Build\\Hashes.sha"
//~WJC_MU2
#endif 

/** 
 * Reference to performance counter string tables
 */
#if DEDICATED_SERVER
	#if GAMENAME==GEARGAME
	#include "..\..\GearGame\Src\GearGamePerfCounters.rc"
    #elif GAMENAME==FORTRESSGAME
 	#include "..\..\FortressBase\Src\FortressBasePerfCounters.rc"
	#endif
#endif

//#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
//#endif    // not APSTUDIO_INVOKED
