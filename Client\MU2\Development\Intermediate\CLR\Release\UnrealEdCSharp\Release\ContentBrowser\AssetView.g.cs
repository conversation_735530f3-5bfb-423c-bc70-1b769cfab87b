﻿#pragma checksum "ContentBrowser\AssetView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3ED7FEDF1D983F8EDCEBD32F5E6FF14FA61C11AA"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using ContentBrowser;
using ContentBrowser.AssetViewValueConverters;
using CustomControls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ContentBrowser {
    
    
    /// <summary>
    /// AssetView
    /// </summary>
    public partial class AssetView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 4 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.AssetView This;
        
        #line default
        #line hidden
        
        
        #line 104 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.YesNoPrompt mProceedWithRemoveAssetsFromCollection;
        
        #line default
        #line hidden
        
        
        #line 121 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.ImageRadioButton mListOnlyRadioButton;
        
        #line default
        #line hidden
        
        
        #line 128 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.ImageRadioButton mHorizontalSplitRadioButton;
        
        #line default
        #line hidden
        
        
        #line 135 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.ImageRadioButton mVerticalSplitRadioButton;
        
        #line default
        #line hidden
        
        
        #line 142 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.ImageRadioButton mThumbsOnlyRadioButton;
        
        #line default
        #line hidden
        
        
        #line 150 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SlateBorder mZoomBorder;
        
        #line default
        #line hidden
        
        
        #line 155 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.DragSlider m_ZoomDragSlider;
        
        #line default
        #line hidden
        
        
        #line 156 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button m_ZoomWidgetResetButton;
        
        #line default
        #line hidden
        
        
        #line 159 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox m_ThumbnailSizeCombo;
        
        #line default
        #line hidden
        
        
        #line 171 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button mShowSortOptionsButton;
        
        #line default
        #line hidden
        
        
        #line 172 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup mSortOptionsPopup;
        
        #line default
        #line hidden
        
        
        #line 181 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mSort_Ascending;
        
        #line default
        #line hidden
        
        
        #line 182 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mSort_Descending;
        
        #line default
        #line hidden
        
        
        #line 184 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mSort_ByName;
        
        #line default
        #line hidden
        
        
        #line 185 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mSort_ByType;
        
        #line default
        #line hidden
        
        
        #line 186 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mSort_ByTags;
        
        #line default
        #line hidden
        
        
        #line 187 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mSort_ByPath;
        
        #line default
        #line hidden
        
        
        #line 188 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mSort_ByDateAdded;
        
        #line default
        #line hidden
        
        
        #line 189 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mSort_ByMemoryUsage;
        
        #line default
        #line hidden
        
        
        #line 201 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border mAssetViewBorder;
        
        #line default
        #line hidden
        
        
        #line 202 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DockPanel mAssetViewDockPanel;
        
        #line default
        #line hidden
        
        
        #line 213 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.CellSizer mAssetListViewSizer;
        
        #line default
        #line hidden
        
        
        #line 215 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.UnrealListView m_AssetListView;
        
        #line default
        #line hidden
        
        
        #line 247 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridView mGridView;
        
        #line default
        #line hidden
        
        
        #line 250 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mNameColumn;
        
        #line default
        #line hidden
        
        
        #line 252 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mNameColumnHeader;
        
        #line default
        #line hidden
        
        
        #line 253 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mNameColumnHeaderContents;
        
        #line default
        #line hidden
        
        
        #line 265 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mAssetTypeColumn;
        
        #line default
        #line hidden
        
        
        #line 267 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mTypeColumnHeader;
        
        #line default
        #line hidden
        
        
        #line 268 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mTypeColumnHeaderContents;
        
        #line default
        #line hidden
        
        
        #line 278 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mTagsColumn;
        
        #line default
        #line hidden
        
        
        #line 280 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mTagsColumnHeader;
        
        #line default
        #line hidden
        
        
        #line 281 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mTagsColumnHeaderContents;
        
        #line default
        #line hidden
        
        
        #line 291 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mPathColumn;
        
        #line default
        #line hidden
        
        
        #line 293 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mPathColumnHeader;
        
        #line default
        #line hidden
        
        
        #line 294 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mPathColumnHeaderContents;
        
        #line default
        #line hidden
        
        
        #line 304 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mDateAddedColumn;
        
        #line default
        #line hidden
        
        
        #line 305 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mDateAddedColumnHeader;
        
        #line default
        #line hidden
        
        
        #line 306 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mDateAddedColumnHeaderContents;
        
        #line default
        #line hidden
        
        
        #line 315 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mMemoryUsageColumn;
        
        #line default
        #line hidden
        
        
        #line 316 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mMemoryUsageColumnHeader;
        
        #line default
        #line hidden
        
        
        #line 317 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mMemoryUsageColumnHeaderContents;
        
        #line default
        #line hidden
        
        
        #line 326 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mCustomDataColumn0;
        
        #line default
        #line hidden
        
        
        #line 327 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mCustomDataColumn0Header;
        
        #line default
        #line hidden
        
        
        #line 328 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mCustomDataColumn0HeaderContents;
        
        #line default
        #line hidden
        
        
        #line 338 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mCustomDataColumn1;
        
        #line default
        #line hidden
        
        
        #line 339 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mCustomDataColumn1Header;
        
        #line default
        #line hidden
        
        
        #line 340 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mCustomDataColumn1HeaderContents;
        
        #line default
        #line hidden
        
        
        #line 350 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mCustomDataColumn2;
        
        #line default
        #line hidden
        
        
        #line 351 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mCustomDataColumn2Header;
        
        #line default
        #line hidden
        
        
        #line 352 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mCustomDataColumn2HeaderContents;
        
        #line default
        #line hidden
        
        
        #line 362 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mCustomDataColumn3;
        
        #line default
        #line hidden
        
        
        #line 363 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mCustomDataColumn3Header;
        
        #line default
        #line hidden
        
        
        #line 364 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mCustomDataColumn3HeaderContents;
        
        #line default
        #line hidden
        
        
        #line 374 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mCustomDataColumn4;
        
        #line default
        #line hidden
        
        
        #line 375 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mCustomDataColumn4Header;
        
        #line default
        #line hidden
        
        
        #line 376 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mCustomDataColumn4HeaderContents;
        
        #line default
        #line hidden
        
        
        #line 386 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mCustomDataColumn5;
        
        #line default
        #line hidden
        
        
        #line 387 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mCustomDataColumn5Header;
        
        #line default
        #line hidden
        
        
        #line 388 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mCustomDataColumn5HeaderContents;
        
        #line default
        #line hidden
        
        
        #line 398 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mCustomDataColumn6;
        
        #line default
        #line hidden
        
        
        #line 399 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mCustomDataColumn6Header;
        
        #line default
        #line hidden
        
        
        #line 400 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mCustomDataColumn6HeaderContents;
        
        #line default
        #line hidden
        
        
        #line 410 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mCustomDataColumn7;
        
        #line default
        #line hidden
        
        
        #line 411 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mCustomDataColumn7Header;
        
        #line default
        #line hidden
        
        
        #line 412 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mCustomDataColumn7HeaderContents;
        
        #line default
        #line hidden
        
        
        #line 422 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mCustomDataColumn8;
        
        #line default
        #line hidden
        
        
        #line 423 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mCustomDataColumn8Header;
        
        #line default
        #line hidden
        
        
        #line 424 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mCustomDataColumn8HeaderContents;
        
        #line default
        #line hidden
        
        
        #line 434 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumn mCustomDataColumn9;
        
        #line default
        #line hidden
        
        
        #line 435 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridViewColumnHeader mCustomDataColumn9Header;
        
        #line default
        #line hidden
        
        
        #line 436 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SortableColumnHeader mCustomDataColumn9HeaderContents;
        
        #line default
        #line hidden
        
        
        #line 452 "ContentBrowser\AssetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.AssetCanvas m_AssetCanvas;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UnrealEdCSharp;component/contentbrowser/assetview.xaml", System.UriKind.Relative);
            
            #line 1 "ContentBrowser\AssetView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.This = ((ContentBrowser.AssetView)(target));
            return;
            case 2:
            this.mProceedWithRemoveAssetsFromCollection = ((CustomControls.YesNoPrompt)(target));
            return;
            case 3:
            this.mListOnlyRadioButton = ((CustomControls.ImageRadioButton)(target));
            return;
            case 4:
            this.mHorizontalSplitRadioButton = ((CustomControls.ImageRadioButton)(target));
            return;
            case 5:
            this.mVerticalSplitRadioButton = ((CustomControls.ImageRadioButton)(target));
            return;
            case 6:
            this.mThumbsOnlyRadioButton = ((CustomControls.ImageRadioButton)(target));
            return;
            case 7:
            this.mZoomBorder = ((CustomControls.SlateBorder)(target));
            return;
            case 8:
            this.m_ZoomDragSlider = ((CustomControls.DragSlider)(target));
            return;
            case 9:
            this.m_ZoomWidgetResetButton = ((System.Windows.Controls.Button)(target));
            return;
            case 10:
            this.m_ThumbnailSizeCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.mShowSortOptionsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 12:
            this.mSortOptionsPopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 13:
            this.mSort_Ascending = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 14:
            this.mSort_Descending = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 15:
            this.mSort_ByName = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 16:
            this.mSort_ByType = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 17:
            this.mSort_ByTags = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 18:
            this.mSort_ByPath = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 19:
            this.mSort_ByDateAdded = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 20:
            this.mSort_ByMemoryUsage = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 21:
            this.mAssetViewBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 22:
            this.mAssetViewDockPanel = ((System.Windows.Controls.DockPanel)(target));
            return;
            case 23:
            this.mAssetListViewSizer = ((CustomControls.CellSizer)(target));
            return;
            case 24:
            this.m_AssetListView = ((ContentBrowser.UnrealListView)(target));
            return;
            case 26:
            this.mGridView = ((System.Windows.Controls.GridView)(target));
            return;
            case 27:
            this.mNameColumn = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 28:
            this.mNameColumnHeader = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 29:
            this.mNameColumnHeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 30:
            this.mAssetTypeColumn = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 31:
            this.mTypeColumnHeader = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 32:
            this.mTypeColumnHeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 33:
            this.mTagsColumn = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 34:
            this.mTagsColumnHeader = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 35:
            this.mTagsColumnHeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 36:
            this.mPathColumn = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 37:
            this.mPathColumnHeader = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 38:
            this.mPathColumnHeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 39:
            this.mDateAddedColumn = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 40:
            this.mDateAddedColumnHeader = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 41:
            this.mDateAddedColumnHeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 42:
            this.mMemoryUsageColumn = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 43:
            this.mMemoryUsageColumnHeader = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 44:
            this.mMemoryUsageColumnHeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 45:
            this.mCustomDataColumn0 = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 46:
            this.mCustomDataColumn0Header = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 47:
            this.mCustomDataColumn0HeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 48:
            this.mCustomDataColumn1 = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 49:
            this.mCustomDataColumn1Header = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 50:
            this.mCustomDataColumn1HeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 51:
            this.mCustomDataColumn2 = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 52:
            this.mCustomDataColumn2Header = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 53:
            this.mCustomDataColumn2HeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 54:
            this.mCustomDataColumn3 = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 55:
            this.mCustomDataColumn3Header = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 56:
            this.mCustomDataColumn3HeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 57:
            this.mCustomDataColumn4 = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 58:
            this.mCustomDataColumn4Header = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 59:
            this.mCustomDataColumn4HeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 60:
            this.mCustomDataColumn5 = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 61:
            this.mCustomDataColumn5Header = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 62:
            this.mCustomDataColumn5HeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 63:
            this.mCustomDataColumn6 = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 64:
            this.mCustomDataColumn6Header = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 65:
            this.mCustomDataColumn6HeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 66:
            this.mCustomDataColumn7 = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 67:
            this.mCustomDataColumn7Header = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 68:
            this.mCustomDataColumn7HeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 69:
            this.mCustomDataColumn8 = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 70:
            this.mCustomDataColumn8Header = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 71:
            this.mCustomDataColumn8HeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 72:
            this.mCustomDataColumn9 = ((System.Windows.Controls.GridViewColumn)(target));
            return;
            case 73:
            this.mCustomDataColumn9Header = ((System.Windows.Controls.GridViewColumnHeader)(target));
            return;
            case 74:
            this.mCustomDataColumn9HeaderContents = ((CustomControls.SortableColumnHeader)(target));
            return;
            case 75:
            this.m_AssetCanvas = ((ContentBrowser.AssetCanvas)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            System.Windows.EventSetter eventSetter;
            switch (connectionId)
            {
            case 25:
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.FrameworkElement.ContextMenuOpeningEvent;
            
            #line 228 "ContentBrowser\AssetView.xaml"
            eventSetter.Handler = new System.Windows.Controls.ContextMenuEventHandler(this.OnAssetCMOpening);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.FrameworkElement.ContextMenuClosingEvent;
            
            #line 229 "ContentBrowser\AssetView.xaml"
            eventSetter.Handler = new System.Windows.Controls.ContextMenuEventHandler(this.OnAssetCMClosing);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.PreviewMouseLeftButtonDownEvent;
            
            #line 232 "ContentBrowser\AssetView.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseButtonEventHandler(this.ListItem_MouseLeftButtonDown);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.PreviewMouseLeftButtonUpEvent;
            
            #line 233 "ContentBrowser\AssetView.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseButtonEventHandler(this.ListItem_MouseLeftButtonUp);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.PreviewMouseMoveEvent;
            
            #line 234 "ContentBrowser\AssetView.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseEventHandler(this.ListItem_MouseMove);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.MouseEnterEvent;
            
            #line 237 "ContentBrowser\AssetView.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseEventHandler(this.AssetListItem_MouseEnter);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.MouseLeaveEvent;
            
            #line 238 "ContentBrowser\AssetView.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseEventHandler(this.AssetListItem_MouseLeave);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            break;
            }
        }
    }
}

