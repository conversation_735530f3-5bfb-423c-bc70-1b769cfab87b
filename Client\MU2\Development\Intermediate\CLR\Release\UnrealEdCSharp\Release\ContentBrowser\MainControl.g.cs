﻿#pragma checksum "ContentBrowser\MainControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1B2DC08A97D56F56A906833DDE923EE10D3B8E91"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using ContentBrowser;
using CustomControls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ContentBrowser {
    
    
    /// <summary>
    /// MainControl
    /// </summary>
    public partial class MainControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 4 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.MainControl userControl;
        
        #line default
        #line hidden
        
        
        #line 82 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid TopLevelGrid;
        
        #line default
        #line hidden
        
        
        #line 104 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DockPanel mWarningPanel;
        
        #line default
        #line hidden
        
        
        #line 112 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button mDismissWarningButton;
        
        #line default
        #line hidden
        
        
        #line 120 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock mWarningLabel;
        
        #line default
        #line hidden
        
        
        #line 135 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border mLeftPanelBorder;
        
        #line default
        #line hidden
        
        
        #line 138 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton mLeftPanelCollapseTrigger;
        
        #line default
        #line hidden
        
        
        #line 142 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.CellSizer mLeftPanel;
        
        #line default
        #line hidden
        
        
        #line 147 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.SourcesPanel m_SourcesPanel;
        
        #line default
        #line hidden
        
        
        #line 167 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border mCatchAllToolbar;
        
        #line default
        #line hidden
        
        
        #line 170 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button m_BackButton;
        
        #line default
        #line hidden
        
        
        #line 173 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContextMenu mHistoryBackContextMenu;
        
        #line default
        #line hidden
        
        
        #line 178 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button m_ForwardButton;
        
        #line default
        #line hidden
        
        
        #line 181 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContextMenu mHistoryForwardContextMenu;
        
        #line default
        #line hidden
        
        
        #line 191 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button m_CloseButton;
        
        #line default
        #line hidden
        
        
        #line 196 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button m_FloatOrDockButton;
        
        #line default
        #line hidden
        
        
        #line 201 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button m_CloneButton;
        
        #line default
        #line hidden
        
        
        #line 209 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock mItemCountLabel;
        
        #line default
        #line hidden
        
        
        #line 212 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock mQuarantineModeLabel;
        
        #line default
        #line hidden
        
        
        #line 219 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.FilterPanel m_FilterPanel;
        
        #line default
        #line hidden
        
        
        #line 223 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.AssetView m_AssetView;
        
        #line default
        #line hidden
        
        
        #line 226 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border mProgressPanel;
        
        #line default
        #line hidden
        
        
        #line 229 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar mProgress;
        
        #line default
        #line hidden
        
        
        #line 230 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock mAssetViewProgressStats;
        
        #line default
        #line hidden
        
        
        #line 240 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border mRightPanelBorder;
        
        #line default
        #line hidden
        
        
        #line 242 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.CellSizer mRightPanel;
        
        #line default
        #line hidden
        
        
        #line 250 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.AssetInspector mAssetInspector;
        
        #line default
        #line hidden
        
        
        #line 254 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton mRightPanelCollapseTrigger;
        
        #line default
        #line hidden
        
        
        #line 265 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border mNotification;
        
        #line default
        #line hidden
        
        
        #line 273 "ContentBrowser\MainControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock mNotificationLabel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UnrealEdCSharp;component/contentbrowser/maincontrol.xaml", System.UriKind.Relative);
            
            #line 1 "ContentBrowser\MainControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.userControl = ((ContentBrowser.MainControl)(target));
            return;
            case 2:
            
            #line 77 "ContentBrowser\MainControl.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 78 "ContentBrowser\MainControl.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TopLevelGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 4:
            this.mWarningPanel = ((System.Windows.Controls.DockPanel)(target));
            return;
            case 5:
            this.mDismissWarningButton = ((System.Windows.Controls.Button)(target));
            return;
            case 6:
            this.mWarningLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.mLeftPanelBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 8:
            this.mLeftPanelCollapseTrigger = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 9:
            this.mLeftPanel = ((CustomControls.CellSizer)(target));
            return;
            case 10:
            this.m_SourcesPanel = ((ContentBrowser.SourcesPanel)(target));
            return;
            case 11:
            this.mCatchAllToolbar = ((System.Windows.Controls.Border)(target));
            return;
            case 12:
            this.m_BackButton = ((System.Windows.Controls.Button)(target));
            
            #line 170 "ContentBrowser\MainControl.xaml"
            this.m_BackButton.Click += new System.Windows.RoutedEventHandler(this.OnBackButtonClicked);
            
            #line default
            #line hidden
            return;
            case 13:
            this.mHistoryBackContextMenu = ((System.Windows.Controls.ContextMenu)(target));
            
            #line 173 "ContentBrowser\MainControl.xaml"
            this.mHistoryBackContextMenu.Opened += new System.Windows.RoutedEventHandler(this.OnBackHistoryCMOpening);
            
            #line default
            #line hidden
            
            #line 173 "ContentBrowser\MainControl.xaml"
            this.mHistoryBackContextMenu.Closed += new System.Windows.RoutedEventHandler(this.OnBackHistoryCMClosing);
            
            #line default
            #line hidden
            return;
            case 14:
            this.m_ForwardButton = ((System.Windows.Controls.Button)(target));
            
            #line 178 "ContentBrowser\MainControl.xaml"
            this.m_ForwardButton.Click += new System.Windows.RoutedEventHandler(this.OnForwardButtonClicked);
            
            #line default
            #line hidden
            return;
            case 15:
            this.mHistoryForwardContextMenu = ((System.Windows.Controls.ContextMenu)(target));
            
            #line 181 "ContentBrowser\MainControl.xaml"
            this.mHistoryForwardContextMenu.Opened += new System.Windows.RoutedEventHandler(this.OnForwardHistoryCMOpening);
            
            #line default
            #line hidden
            
            #line 181 "ContentBrowser\MainControl.xaml"
            this.mHistoryForwardContextMenu.Closed += new System.Windows.RoutedEventHandler(this.OnForwardHistoryCMClosing);
            
            #line default
            #line hidden
            return;
            case 16:
            this.m_CloseButton = ((System.Windows.Controls.Button)(target));
            return;
            case 17:
            this.m_FloatOrDockButton = ((System.Windows.Controls.Button)(target));
            return;
            case 18:
            this.m_CloneButton = ((System.Windows.Controls.Button)(target));
            return;
            case 19:
            this.mItemCountLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.mQuarantineModeLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.m_FilterPanel = ((ContentBrowser.FilterPanel)(target));
            return;
            case 22:
            this.m_AssetView = ((ContentBrowser.AssetView)(target));
            return;
            case 23:
            this.mProgressPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 24:
            this.mProgress = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 25:
            this.mAssetViewProgressStats = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.mRightPanelBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 27:
            this.mRightPanel = ((CustomControls.CellSizer)(target));
            return;
            case 28:
            this.mAssetInspector = ((ContentBrowser.AssetInspector)(target));
            return;
            case 29:
            this.mRightPanelCollapseTrigger = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 30:
            this.mNotification = ((System.Windows.Controls.Border)(target));
            return;
            case 31:
            this.mNotificationLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

