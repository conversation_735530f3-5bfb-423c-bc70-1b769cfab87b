﻿  UBT Arguments: MU2Game Win32 Release -vs2015 -<PERSON><PERSON><PERSON> WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output ../../Binaries/Win32/MU2Game.exe -DEPLOY
  
  
  
  ========== UnrealBuildTool activate VisualStudio2015 compile ==========
  
  
  
  PCLaunch.rc
  Unity_MU2ItemInfoTableEtAl.cpp
  Unity_MU2WorldEtAl.cpp
  Unity_MU2Network_RegisterEtAl.cpp
.\../../../../Shared/Version.h(64): fatal error RC1020: unexpected '#endif'
  
EXEC : UBT error : Failed to produce item: F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.exe
EXEC : UBT error : Failed to produce item: F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.lib
EXEC : UBT error : Failed to produce item: F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.exp
EXEC : UBT error : Failed to produce item: F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.pdb
  Could not connect to database.
  [10:01] UBT execution time: 25.21 seconds, 0.00 seconds linking
EXEC : warning : Communicating with the Database took 4.9544636 seconds.
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.MakeFile.Targets(45,5): error MSB3073: 命令“@cd ..
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.MakeFile.Targets(45,5): error MSB3073: @call Targets/Build.bat MU2Game Win32 Release -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win32/MU2Game.exe"”已退出，代码为 1。
