﻿  UBT Arguments: MU2Game Win32 Release -vs2015 -<PERSON>FINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output ../../Binaries/Win32/MU2Game.exe -DEPLOY
  
  
  
  ========== UnrealBuildTool activate VisualStudio2015 compile ==========
  
  
  
  Unity_NewProjectCLREtAl.cpp
  UnrealEd.h.PCH.cpp
  Unity_MU2SystemSettingsEtAl.cpp
  CorePrivate.h.PCH.cpp
  EnginePrivate.h.PCH.cpp
  MU2GamePrivate.h.PCH.cpp
  Unity_PIBEtAl.cpp
  Unity_SecondaryViewportClientEtAl.cpp
  Unity_GFxUIMovieCustomImporterEtAl.cpp
  Unity_MU2WeaponEditPageEtAl.cpp
  Unity_DlgViewSetEtAl.cpp
  Unity_GwNavEditorEtAl.cpp
  Unity_VoiceInterfaceSteamworksEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\GameFramework\Src\GameCrowd.cpp(806): warning C4305: “初始化”: 从“<unnamed-enum-MAXINT>”到“FLOAT”截断
  Unity_MU2WxInterpEditorEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): warning C5205: 删除具有非虚拟析构函数的抽象类 "ViewSetData" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(30): note: 参见“ViewSetData”的声明
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(240): note: 查看对正在编译的 类 模板 实例化“ViewSetDataList<StaticMeshViewSetData>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(289): note: 在编译 类 模板 成员函数“ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList(void)”时
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Src\DlgViewSet.cpp(273): note: 请参阅 "WxDlgStaticMeshViewSet::WxDlgStaticMeshViewSet" 中对 "ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList" 的第一个引用
  Unity_GwNavWorldRenderingSceneProxyEtAl.cpp
  Unity_WebServerEtAl.cpp
  Unity_UnVcWin32EtAl.cpp
  Unity_UnMiscEtAl.cpp
  Unity_ScaleformMovieEtAl.cpp
  Unity_UnEngineEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\Core\Src\UnVcWin32.cpp(1671): warning C4996: 'GetVersionExW': 被声明为已否决
  Unity_ImageReflectionRenderingEtAl.cpp
  Unity_UnrealEdSrvEtAl.cpp
  Unity_GenericBrowserTypesEtAl.cpp
  Unity_MeshPaintEdModeEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): warning C5205: 删除具有非虚拟析构函数的抽象类 "ViewSetData" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(30): note: 参见“ViewSetData”的声明
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(240): note: 查看对正在编译的 类 模板 实例化“ViewSetDataList<StaticMeshViewSetData>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(289): note: 在编译 类 模板 成员函数“ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList(void)”时
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Inc\MU2AgentEditPage.h(74): note: 查看对正在编译的 类 模板 实例化“MU2FxSearchComponent<MU2ItemList::ItemEntry>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(186): note: 在编译 类 模板 成员函数“void MU2FxSearchComponent<MU2ItemList::ItemEntry>::AddFilter(MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter *)”时
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2AgentEditPage.cpp(113): note: 请参阅 "MU2AgentEditPage::MU2AgentEditPage" 中对 "MU2FxSearchComponent<MU2ItemList::ItemEntry>::AddFilter" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 查看对正在编译的函数 模板 实例化“std::shared_ptr<MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter>::shared_ptr<MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter,0>(_Ux *)”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1589): note: 查看对正在编译的 类 模板 实例化“std::_Ref_count<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1186): note: 在编译 类 模板 成员函数“void std::_Ref_count<_Ux>::_Destroy(void) noexcept”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter
          ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): note: 模板实例化上下文(最早的实例化上下文)为
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 查看对正在编译的 类 模板 实例化“std::_Temporary_owner<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1529): note: 在编译 类 模板 成员函数“std::_Temporary_owner<_Ux>::~_Temporary_owner(void)”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 请参阅 "std::shared_ptr<MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter>::shared_ptr" 中对 "std::_Temporary_owner<_Ux>::~_Temporary_owner" 的第一个引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter
          ]
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 请参阅 "MU2FxSearchComponent<MU2ItemList::ItemEntry>::AddFilter" 中对 "std::shared_ptr<MU2FxSearchComponent<MU2ItemList::ItemEntry>::SearchFilter>::shared_ptr" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2AgentEditPage.cpp(113): note: 请参阅 "MU2AgentEditPage::MU2AgentEditPage" 中对 "MU2FxSearchComponent<MU2ItemList::ItemEntry>::AddFilter" 的第一个引用
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Inc\MU2AgentEditPage.h(73): note: 查看对正在编译的 类 模板 实例化“MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(186): note: 在编译 类 模板 成员函数“void MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::AddFilter(MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter *)”时
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2AgentEditPage.cpp(67): note: 请参阅 "MU2AgentEditPage::MU2AgentEditPage" 中对 "MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::AddFilter" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 查看对正在编译的函数 模板 实例化“std::shared_ptr<MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter>::shared_ptr<MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter,0>(_Ux *)”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1589): note: 查看对正在编译的 类 模板 实例化“std::_Ref_count<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1186): note: 在编译 类 模板 成员函数“void std::_Ref_count<_Ux>::_Destroy(void) noexcept”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter
          ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): note: 模板实例化上下文(最早的实例化上下文)为
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 查看对正在编译的 类 模板 实例化“std::_Temporary_owner<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1529): note: 在编译 类 模板 成员函数“std::_Temporary_owner<_Ux>::~_Temporary_owner(void)”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 请参阅 "std::shared_ptr<MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter>::shared_ptr" 中对 "std::_Temporary_owner<_Ux>::~_Temporary_owner" 的第一个引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter
          ]
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 请参阅 "MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::AddFilter" 中对 "std::shared_ptr<MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::SearchFilter>::shared_ptr" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2AgentEditPage.cpp(67): note: 请参阅 "MU2AgentEditPage::MU2AgentEditPage" 中对 "MU2FxSearchComponent<MU2FxSetAgentList::AgentEntry>::AddFilter" 的第一个引用
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Inc\MU2FxModelEditPage.h(150): note: 查看对正在编译的 类 模板 实例化“MU2FxSearchComponent<UMU2ViewAvatar>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(186): note: 在编译 类 模板 成员函数“void MU2FxSearchComponent<UMU2ViewAvatar>::AddFilter(MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter *)”时
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxModelEditPage.cpp(75): note: 请参阅 "MU2FxModelEditPage::MU2FxModelEditPage" 中对 "MU2FxSearchComponent<UMU2ViewAvatar>::AddFilter" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 查看对正在编译的函数 模板 实例化“std::shared_ptr<MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter>::shared_ptr<MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter,0>(_Ux *)”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1589): note: 查看对正在编译的 类 模板 实例化“std::_Ref_count<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1186): note: 在编译 类 模板 成员函数“void std::_Ref_count<_Ux>::_Destroy(void) noexcept”时
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter
          ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): note: 模板实例化上下文(最早的实例化上下文)为
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 查看对正在编译的 类 模板 实例化“std::_Temporary_owner<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1529): note: 在编译 类 模板 成员函数“std::_Temporary_owner<_Ux>::~_Temporary_owner(void)”时
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 请参阅 "std::shared_ptr<MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter>::shared_ptr" 中对 "std::_Temporary_owner<_Ux>::~_Temporary_owner" 的第一个引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter
          ]
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 请参阅 "MU2FxSearchComponent<UMU2ViewAvatar>::AddFilter" 中对 "std::shared_ptr<MU2FxSearchComponent<UMU2ViewAvatar>::SearchFilter>::shared_ptr" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxModelEditPage.cpp(75): note: 请参阅 "MU2FxModelEditPage::MU2FxModelEditPage" 中对 "MU2FxSearchComponent<UMU2ViewAvatar>::AddFilter" 的第一个引用
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Inc\MU2FxSetPlayPage.h(106): note: 查看对正在编译的 类 模板 实例化“MU2FxSearchComponent<MU2FxSkillList::SkillEntry>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(186): note: 在编译 类 模板 成员函数“void MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::AddFilter(MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter *)”时
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSetPlayPage.cpp(89): note: 请参阅 "MU2FxSetPlayPage::MU2FxSetPlayPage" 中对 "MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::AddFilter" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 查看对正在编译的函数 模板 实例化“std::shared_ptr<MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter>::shared_ptr<MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter,0>(_Ux *)”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1589): note: 查看对正在编译的 类 模板 实例化“std::_Ref_count<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1186): note: 在编译 类 模板 成员函数“void std::_Ref_count<_Ux>::_Destroy(void) noexcept”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter
          ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): note: 模板实例化上下文(最早的实例化上下文)为
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 查看对正在编译的 类 模板 实例化“std::_Temporary_owner<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1529): note: 在编译 类 模板 成员函数“std::_Temporary_owner<_Ux>::~_Temporary_owner(void)”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 请参阅 "std::shared_ptr<MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter>::shared_ptr" 中对 "std::_Temporary_owner<_Ux>::~_Temporary_owner" 的第一个引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter
          ]
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 请参阅 "MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::AddFilter" 中对 "std::shared_ptr<MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::SearchFilter>::shared_ptr" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSetPlayPage.cpp(89): note: 请参阅 "MU2FxSetPlayPage::MU2FxSetPlayPage" 中对 "MU2FxSearchComponent<MU2FxSkillList::SkillEntry>::AddFilter" 的第一个引用
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Inc\MU2WeaponEditPage.h(170): note: 查看对正在编译的 类 模板 实例化“MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(186): note: 在编译 类 模板 成员函数“void MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::AddFilter(MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter *)”时
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2WeaponEditPage.cpp(63): note: 请参阅 "MU2WeaponEditPage::MU2WeaponEditPage" 中对 "MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::AddFilter" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 查看对正在编译的函数 模板 实例化“std::shared_ptr<MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter>::shared_ptr<MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter,0>(_Ux *)”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1589): note: 查看对正在编译的 类 模板 实例化“std::_Ref_count<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1186): note: 在编译 类 模板 成员函数“void std::_Ref_count<_Ux>::_Destroy(void) noexcept”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter
          ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): note: 模板实例化上下文(最早的实例化上下文)为
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 查看对正在编译的 类 模板 实例化“std::_Temporary_owner<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1529): note: 在编译 类 模板 成员函数“std::_Temporary_owner<_Ux>::~_Temporary_owner(void)”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 请参阅 "std::shared_ptr<MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter>::shared_ptr" 中对 "std::_Temporary_owner<_Ux>::~_Temporary_owner" 的第一个引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter
          ]
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 请参阅 "MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::AddFilter" 中对 "std::shared_ptr<MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::SearchFilter>::shared_ptr" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2WeaponEditPage.cpp(63): note: 请参阅 "MU2WeaponEditPage::MU2WeaponEditPage" 中对 "MU2FxSearchComponent<MU2WeaponListCtrl::ItemEntry>::AddFilter" 的第一个引用
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Inc\MU2CustomizeEditPage.h(191): note: 查看对正在编译的 类 模板 实例化“MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(186): note: 在编译 类 模板 成员函数“void MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::AddFilter(MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter *)”时
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2CustomizeEditPage.cpp(73): note: 请参阅 "MU2CustomizeEditPage::MU2CustomizeEditPage" 中对 "MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::AddFilter" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 查看对正在编译的函数 模板 实例化“std::shared_ptr<MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter>::shared_ptr<MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter,0>(_Ux *)”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1589): note: 查看对正在编译的 类 模板 实例化“std::_Ref_count<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1186): note: 在编译 类 模板 成员函数“void std::_Ref_count<_Ux>::_Destroy(void) noexcept”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter
          ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): note: 模板实例化上下文(最早的实例化上下文)为
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 查看对正在编译的 类 模板 实例化“std::_Temporary_owner<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1529): note: 在编译 类 模板 成员函数“std::_Temporary_owner<_Ux>::~_Temporary_owner(void)”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 请参阅 "std::shared_ptr<MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter>::shared_ptr" 中对 "std::_Temporary_owner<_Ux>::~_Temporary_owner" 的第一个引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter
          ]
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 请参阅 "MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::AddFilter" 中对 "std::shared_ptr<MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::SearchFilter>::shared_ptr" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2CustomizeEditPage.cpp(73): note: 请参阅 "MU2CustomizeEditPage::MU2CustomizeEditPage" 中对 "MU2FxSearchComponent<MU2CustomizeListCtrl::CustomEntry>::AddFilter" 的第一个引用
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Inc\MU2FxAnimPage.h(31): note: 查看对正在编译的 类 模板 实例化“MU2FxSearchComponent<UMU2ViewSkillAnimWrap>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(186): note: 在编译 类 模板 成员函数“void MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::AddFilter(MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter *)”时
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxAnimPage.cpp(63): note: 请参阅 "MU2FxAnimPage::MU2FxAnimPage" 中对 "MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::AddFilter" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 查看对正在编译的函数 模板 实例化“std::shared_ptr<MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter>::shared_ptr<MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter,0>(_Ux *)”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1589): note: 查看对正在编译的 类 模板 实例化“std::_Ref_count<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1186): note: 在编译 类 模板 成员函数“void std::_Ref_count<_Ux>::_Destroy(void) noexcept”时
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter
          ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): note: 模板实例化上下文(最早的实例化上下文)为
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 查看对正在编译的 类 模板 实例化“std::_Temporary_owner<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1529): note: 在编译 类 模板 成员函数“std::_Temporary_owner<_Ux>::~_Temporary_owner(void)”时
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 请参阅 "std::shared_ptr<MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter>::shared_ptr" 中对 "std::_Temporary_owner<_Ux>::~_Temporary_owner" 的第一个引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter
          ]
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 请参阅 "MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::AddFilter" 中对 "std::shared_ptr<MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::SearchFilter>::shared_ptr" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxAnimPage.cpp(63): note: 请参阅 "MU2FxAnimPage::MU2FxAnimPage" 中对 "MU2FxSearchComponent<UMU2ViewSkillAnimWrap>::AddFilter" 的第一个引用
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Inc\MU2FxSoundPage.h(32): note: 查看对正在编译的 类 模板 实例化“MU2FxSearchComponent<UMU2ViewSkillSoundWrap>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(186): note: 在编译 类 模板 成员函数“void MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::AddFilter(MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter *)”时
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSoundPage.cpp(60): note: 请参阅 "MU2FxSoundPage::MU2FxSoundPage" 中对 "MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::AddFilter" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 查看对正在编译的函数 模板 实例化“std::shared_ptr<MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter>::shared_ptr<MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter,0>(_Ux *)”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1589): note: 查看对正在编译的 类 模板 实例化“std::_Ref_count<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1186): note: 在编译 类 模板 成员函数“void std::_Ref_count<_Ux>::_Destroy(void) noexcept”时
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter
          ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): note: 模板实例化上下文(最早的实例化上下文)为
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 查看对正在编译的 类 模板 实例化“std::_Temporary_owner<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1529): note: 在编译 类 模板 成员函数“std::_Temporary_owner<_Ux>::~_Temporary_owner(void)”时
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 请参阅 "std::shared_ptr<MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter>::shared_ptr" 中对 "std::_Temporary_owner<_Ux>::~_Temporary_owner" 的第一个引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter
          ]
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 请参阅 "MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::AddFilter" 中对 "std::shared_ptr<MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::SearchFilter>::shared_ptr" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSoundPage.cpp(60): note: 请参阅 "MU2FxSoundPage::MU2FxSoundPage" 中对 "MU2FxSearchComponent<UMU2ViewSkillSoundWrap>::AddFilter" 的第一个引用
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<FPawnListInfo>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<FPawnListInfo>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Inc\MU2FxAnimSoundPage.h(86): note: 查看对正在编译的 类 模板 实例化“MU2FxSearchComponent<FPawnListInfo>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(186): note: 在编译 类 模板 成员函数“void MU2FxSearchComponent<FPawnListInfo>::AddFilter(MU2FxSearchComponent<FPawnListInfo>::SearchFilter *)”时
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxAnimSoundPage.cpp(82): note: 请参阅 "MU2FxAnimSoundPage::MU2FxAnimSoundPage" 中对 "MU2FxSearchComponent<FPawnListInfo>::AddFilter" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 查看对正在编译的函数 模板 实例化“std::shared_ptr<MU2FxSearchComponent<FPawnListInfo>::SearchFilter>::shared_ptr<MU2FxSearchComponent<FPawnListInfo>::SearchFilter,0>(_Ux *)”的引用
          with
          [
              _Ux=MU2FxSearchComponent<FPawnListInfo>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1589): note: 查看对正在编译的 类 模板 实例化“std::_Ref_count<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<FPawnListInfo>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1186): note: 在编译 类 模板 成员函数“void std::_Ref_count<_Ux>::_Destroy(void) noexcept”时
          with
          [
              _Ux=MU2FxSearchComponent<FPawnListInfo>::SearchFilter
          ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<FPawnListInfo>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<FPawnListInfo>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): note: 模板实例化上下文(最早的实例化上下文)为
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 查看对正在编译的 类 模板 实例化“std::_Temporary_owner<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<FPawnListInfo>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1529): note: 在编译 类 模板 成员函数“std::_Temporary_owner<_Ux>::~_Temporary_owner(void)”时
          with
          [
              _Ux=MU2FxSearchComponent<FPawnListInfo>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 请参阅 "std::shared_ptr<MU2FxSearchComponent<FPawnListInfo>::SearchFilter>::shared_ptr" 中对 "std::_Temporary_owner<_Ux>::~_Temporary_owner" 的第一个引用
          with
          [
              _Ux=MU2FxSearchComponent<FPawnListInfo>::SearchFilter
          ]
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 请参阅 "MU2FxSearchComponent<FPawnListInfo>::AddFilter" 中对 "std::shared_ptr<MU2FxSearchComponent<FPawnListInfo>::SearchFilter>::shared_ptr" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxAnimSoundPage.cpp(82): note: 请参阅 "MU2FxAnimSoundPage::MU2FxAnimSoundPage" 中对 "MU2FxSearchComponent<FPawnListInfo>::AddFilter" 的第一个引用
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<UMU2FxViewList>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<UMU2FxViewList>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Inc\MU2FxSetEditPage.h(32): note: 查看对正在编译的 类 模板 实例化“MU2FxSearchComponent<UMU2FxViewList>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(186): note: 在编译 类 模板 成员函数“void MU2FxSearchComponent<UMU2FxViewList>::AddFilter(MU2FxSearchComponent<UMU2FxViewList>::SearchFilter *)”时
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSetEditPage.cpp(289): note: 请参阅 "MU2FxSetEditPage::MU2FxSetEditPage" 中对 "MU2FxSearchComponent<UMU2FxViewList>::AddFilter" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 查看对正在编译的函数 模板 实例化“std::shared_ptr<MU2FxSearchComponent<UMU2FxViewList>::SearchFilter>::shared_ptr<MU2FxSearchComponent<UMU2FxViewList>::SearchFilter,0>(_Ux *)”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2FxViewList>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1589): note: 查看对正在编译的 类 模板 实例化“std::_Ref_count<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2FxViewList>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1186): note: 在编译 类 模板 成员函数“void std::_Ref_count<_Ux>::_Destroy(void) noexcept”时
          with
          [
              _Ux=MU2FxSearchComponent<UMU2FxViewList>::SearchFilter
          ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<UMU2FxViewList>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<UMU2FxViewList>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): note: 模板实例化上下文(最早的实例化上下文)为
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 查看对正在编译的 类 模板 实例化“std::_Temporary_owner<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2FxViewList>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1529): note: 在编译 类 模板 成员函数“std::_Temporary_owner<_Ux>::~_Temporary_owner(void)”时
          with
          [
              _Ux=MU2FxSearchComponent<UMU2FxViewList>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 请参阅 "std::shared_ptr<MU2FxSearchComponent<UMU2FxViewList>::SearchFilter>::shared_ptr" 中对 "std::_Temporary_owner<_Ux>::~_Temporary_owner" 的第一个引用
          with
          [
              _Ux=MU2FxSearchComponent<UMU2FxViewList>::SearchFilter
          ]
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 请参阅 "MU2FxSearchComponent<UMU2FxViewList>::AddFilter" 中对 "std::shared_ptr<MU2FxSearchComponent<UMU2FxViewList>::SearchFilter>::shared_ptr" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSetEditPage.cpp(289): note: 请参阅 "MU2FxSetEditPage::MU2FxSetEditPage" 中对 "MU2FxSearchComponent<UMU2FxViewList>::AddFilter" 的第一个引用
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1187): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Inc\MU2GibsEditPage.h(90): note: 查看对正在编译的 类 模板 实例化“MU2FxSearchComponent<MU2FxBuffList::BuffEntry>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(186): note: 在编译 类 模板 成员函数“void MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::AddFilter(MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter *)”时
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2GibsEditPage.cpp(63): note: 请参阅 "MU2GibsEditPage::MU2GibsEditPage" 中对 "MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::AddFilter" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 查看对正在编译的函数 模板 实例化“std::shared_ptr<MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter>::shared_ptr<MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter,0>(_Ux *)”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1589): note: 查看对正在编译的 类 模板 实例化“std::_Ref_count<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1186): note: 在编译 类 模板 成员函数“void std::_Ref_count<_Ux>::_Destroy(void) noexcept”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter
          ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): warning C5205: 删除具有非虚拟析构函数的抽象类 "MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(13): note: 参见“MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter”的声明
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1530): note: 模板实例化上下文(最早的实例化上下文)为
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 查看对正在编译的 类 模板 实例化“std::_Temporary_owner<_Ux>”的引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1529): note: 在编译 类 模板 成员函数“std::_Temporary_owner<_Ux>::~_Temporary_owner(void)”时
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter
          ]
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1588): note: 请参阅 "std::shared_ptr<MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter>::shared_ptr" 中对 "std::_Temporary_owner<_Ux>::~_Temporary_owner" 的第一个引用
          with
          [
              _Ux=MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter
          ]
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2FxSearchComponent.hpp(188): note: 请参阅 "MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::AddFilter" 中对 "std::shared_ptr<MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::SearchFilter>::shared_ptr" 的第一个引用
  F:\Release_Branch\Client\MU2\Development\Src\MU2Editor\Src\MU2GibsEditPage.cpp(63): note: 请参阅 "MU2GibsEditPage::MU2GibsEditPage" 中对 "MU2FxSearchComponent<MU2FxBuffList::BuffEntry>::AddFilter" 的第一个引用
  Unity_UnEdModesEtAl.cpp
  Unity_EditorFrameEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): warning C5205: 删除具有非虚拟析构函数的抽象类 "ViewSetData" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(30): note: 参见“ViewSetData”的声明
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(240): note: 查看对正在编译的 类 模板 实例化“ViewSetDataList<StaticMeshViewSetData>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(289): note: 在编译 类 模板 成员函数“ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList(void)”时
  Unity_UContentCommandletsEtAl.cpp
  Unity_LightmassEtAl.cpp
  Unity_DlgLightingResultsEtAl.cpp
  Unity_PlayLevelEtAl.cpp
  Unity_SceneManagerEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): warning C5205: 删除具有非虚拟析构函数的抽象类 "ViewSetData" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(30): note: 参见“ViewSetData”的声明
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(240): note: 查看对正在编译的 类 模板 实例化“ViewSetDataList<StaticMeshViewSetData>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(289): note: 在编译 类 模板 成员函数“ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList(void)”时
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): warning C5205: 删除具有非虚拟析构函数的抽象类 "ViewSetData" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(30): note: 参见“ViewSetData”的声明
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(240): note: 查看对正在编译的 类 模板 实例化“ViewSetDataList<StaticMeshViewSetData>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(289): note: 在编译 类 模板 成员函数“ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList(void)”时
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): warning C5205: 删除具有非虚拟析构函数的抽象类 "ViewSetData" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(30): note: 参见“ViewSetData”的声明
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(240): note: 查看对正在编译的 类 模板 实例化“ViewSetDataList<StaticMeshViewSetData>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(289): note: 在编译 类 模板 成员函数“ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList(void)”时
  Unity_StaticMeshEditorEtAl.cpp
  Unity_TaskBrowserEtAl.cpp
  Unity_PropertyUtilsEtAl.cpp
  Unity_LayerBrowserEtAl.cpp
  Unity_FoliageEdModeEtAl.cpp
  Unity_CurveEdEtAl.cpp
  Unity_SourceControlEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): warning C5205: 删除具有非虚拟析构函数的抽象类 "ViewSetData" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(30): note: 参见“ViewSetData”的声明
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(240): note: 查看对正在编译的 类 模板 实例化“ViewSetDataList<StaticMeshViewSetData>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(289): note: 在编译 类 模板 成员函数“ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList(void)”时
  Unity_AnimSetViewerToolsEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): warning C5205: 删除具有非虚拟析构函数的抽象类 "ViewSetData" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(30): note: 参见“ViewSetData”的声明
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(240): note: 查看对正在编译的 类 模板 实例化“ViewSetDataList<StaticMeshViewSetData>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(289): note: 在编译 类 模板 成员函数“ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList(void)”时
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): warning C5205: 删除具有非虚拟析构函数的抽象类 "ViewSetData" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(30): note: 参见“ViewSetData”的声明
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(240): note: 查看对正在编译的 类 模板 实例化“ViewSetDataList<StaticMeshViewSetData>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(289): note: 在编译 类 模板 成员函数“ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList(void)”时
  Unity_UnObjectToolsEtAl.cpp
  Unity_UnEdExpEtAl.cpp
  Unity_UnEdSrvEtAl.cpp
  Unity_LandscapeEdModeEtAl.cpp
  Unity_UnPackageUtilitiesEtAl.cpp
  Unity_EditorBuildUtilsEtAl.cpp
  Unity_UMakeCommandletEtAl.cpp
  Unity_UnContentCookersEtAl.cpp
  Unity_GameStatsReportEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Src\TaskDatabaseThread.cpp(316): warning C5205: 删除具有非虚拟析构函数的抽象类 "FTaskDatabaseProviderInterface" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\TaskDatabaseDefs.h(177): note: 参见“FTaskDatabaseProviderInterface”的声明
  Unity_MaterialEditorToolBarEtAl.cpp
  Unity_UnEdFactEtAl.cpp
  Unity_AttachmentEditorEtAl.cpp
  Unity_UnGameEtAl.cpp
  Unity_UnEdViewportEtAl.cpp
  Unity_KismetEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): warning C5205: 删除具有非虚拟析构函数的抽象类 "ViewSetData" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(30): note: 参见“ViewSetData”的声明
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(240): note: 查看对正在编译的 类 模板 实例化“ViewSetDataList<StaticMeshViewSetData>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(289): note: 在编译 类 模板 成员函数“ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList(void)”时
  Unity_AnimSetViewerMainEtAl.cpp
  Unity_UnLevelEtAl.cpp
  Unity_UnSequenceEtAl.cpp
  Unity_UnStaticMeshEditEtAl.cpp
  Unity_NewMaterialEditorEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): warning C5205: 删除具有非虚拟析构函数的抽象类 "ViewSetData" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(30): note: 参见“ViewSetData”的声明
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(296): note: 模板实例化上下文(最早的实例化上下文)为
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(240): note: 查看对正在编译的 类 模板 实例化“ViewSetDataList<StaticMeshViewSetData>”的引用
  F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Inc\DlgViewSet.h(289): note: 在编译 类 模板 成员函数“ViewSetDataList<StaticMeshViewSetData>::~ViewSetDataList(void)”时
  Unity_ViewportsEtAl.cpp
  Unity_UnFbxSkeletalMeshImportEtAl.cpp
  Unity_InterpEditorToolsEtAl.cpp
  Unity_UnGeomModifiersEtAl.cpp
  Unity_UnContentStreamingEtAl.cpp
  Unity_InterpEditorDrawEtAl.cpp
  Unity_DwTriovizImplEtAl.cpp
  Unity_CascadeModuleConversionEtAl.cpp
  Unity_NewProjectEtAl.cpp
  Unity_UnTexCompressEtAl.cpp
  Unity_TerrainEditorEtAl.cpp
  Unity_UnPenLevEtAl.cpp
  Unity_UnScrComEtAl.cpp
  Unity_UnActorEtAl.cpp
  Unity_PrimitiveComponentEtAl.cpp
  Unity_DynamicLightEnvironmentComponentEtAl.cpp
  Unity_DecalComponentEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\UnrealEd\Src\UnScrCom.cpp(13578): warning C5205: 删除具有非虚拟析构函数的抽象类 "FTransactionBase" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\Core\Inc\Core.h(1224): note: 参见“FTransactionBase”的声明
  Unity_UnPlayerEtAl.cpp
  Unity_Texture2DEtAl.cpp
  Unity_ScriptPlatformInterfaceEtAl.cpp
  Unity_UnCanvasEtAl.cpp
  Unity_WorldAttractorEtAl.cpp
  Unity_UnSkeletalRenderCPUSkinEtAl.cpp
  Unity_LightComponentEtAl.cpp
  Unity_UnNavigationConstraintsAndGoalsEtAl.cpp
  Unity_SceneRenderingEtAl.cpp
  Unity_BatchedElementsEtAl.cpp
  Unity_FluidSurfaceRenderingEtAl.cpp
  Unity_MaterialExpressionsEtAl.cpp
  Unity_SplineLoftEtAl.cpp
  Unity_SpeedTreeComponentEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\Engine\Src\NvApexScene.cpp(133): warning C5205: 删除具有非虚拟析构函数的抽象类 "FIApexActor" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\Engine\Src\NvApexScene.h(181): note: 参见“FIApexActor”的声明
  Unity_UnTerrainEtAl.cpp
  Unity_UnDistributionsEtAl.cpp
  Unity_UnStaticMeshEtAl.cpp
  Unity_LightShaftRenderingEtAl.cpp
  Unity_LandscapeEditInterfaceEtAl.cpp
  Unity_VideoDeviceEtAl.cpp
  Unity_UnAnimTreeEtAl.cpp
  Unity_XnaForceFeedbackManagerEtAl.cpp
  Unity_FCallbackDeviceEtAl.cpp
  Unity_UnRouteEtAl.cpp
  Unity_UnParticleSystemRenderEtAl.cpp
  Unity_MU2GameClientEtAl.cpp
  Unity_MU2StorageInfoTableEtAl.cpp
  Unity_MU2PcPawnEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\WinDrv\Src\WinClient.cpp(664): warning C4644: 常量表达式中基于宏的 offsetof 模式的用法不标准；请改用 C++ 标准库中定义的 offsetof
F:\Release_Branch\Client\MU2\Development\Src\WinDrv\Src\WinClient.cpp(669): warning C4644: 常量表达式中基于宏的 offsetof 模式的用法不标准；请改用 C++ 标准库中定义的 offsetof
F:\Release_Branch\Client\MU2\Development\Src\WinDrv\Src\WinClient.cpp(674): warning C4644: 常量表达式中基于宏的 offsetof 模式的用法不标准；请改用 C++ 标准库中定义的 offsetof
F:\Release_Branch\Client\MU2\Development\Src\WinDrv\Src\WinClient.cpp(729): warning C4644: 常量表达式中基于宏的 offsetof 模式的用法不标准；请改用 C++ 标准库中定义的 offsetof
F:\Release_Branch\Client\MU2\Development\Src\WinDrv\Src\WinClient.cpp(734): warning C4644: 常量表达式中基于宏的 offsetof 模式的用法不标准；请改用 C++ 标准库中定义的 offsetof
F:\Release_Branch\Client\MU2\Development\Src\WinDrv\Src\WinClient.cpp(739): warning C4644: 常量表达式中基于宏的 offsetof 模式的用法不标准；请改用 C++ 标准库中定义的 offsetof
  Unity_MU2ItemInfoTableEtAl.cpp
F:\Release_Branch\Shared/Version.h(81): fatal error C1070: 文件“F:\Release_Branch\Shared\Version.h”中的 #if/#endif 对不匹配
  Unity_MU2ChatManagerEtAl.cpp
  Unity_MU2EntityEtAl.cpp
  Unity_MU2NetworkEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\MU2Game\Src\MU2ChatManager.cpp(1022): warning C5205: 删除具有非虚拟析构函数的抽象类 "FMU2CommandFunctor" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Game\Inc\MU2CommandFunctor.h(12): note: 参见“FMU2CommandFunctor”的声明
F:\Release_Branch\Client\MU2\Development\Src\MU2Game\Src\MU2ChatManager.cpp(1031): warning C5205: 删除具有非虚拟析构函数的抽象类 "FMU2CommandFunctor" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Game\Inc\MU2CommandFunctor.h(12): note: 参见“FMU2CommandFunctor”的声明
  Unity_MU2WorldEtAl.cpp
  Unity_MU2UIGFxIngameEtAl.cpp
F:\Release_Branch\Shared/Version.h(81): fatal error C1070: 文件“F:\Release_Branch\Shared\Version.h”中的 #if/#endif 对不匹配
  Unity_MU2QuestEtAl.cpp
  Unity_MU2ActionSkillEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\MU2Game\Src\MU2Entity.cpp(5114): warning C5205: 删除具有非虚拟析构函数的抽象类 "FMU2TooltipMsgFunctor" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Game\Inc\MU2CommandFunctor.h(810): note: 参见“FMU2TooltipMsgFunctor”的声明
  Unity_MU2Network_RegisterEtAl.cpp
  Unity_MU2Network_GameEtAl.cpp
F:\Release_Branch\Shared/Version.h(81): fatal error C1070: 文件“F:\Release_Branch\Shared\Version.h”中的 #if/#endif 对不匹配
  Unity_MU2UITooltipEtAl.cpp
  Unity_MU2ZonePathGuideTableEtAl.cpp
  Unity_ParticleBeam2EmitterInstanceEtAl.cpp
  Unity_UnInterpolationEtAl.cpp
  Unity_UnAudioNodesEtAl.cpp
  Unity_WinDrvEtAl.cpp
  Unity_NxTornadoAngularForceFieldEtAl.cpp
  Unity_ShadowRenderingEtAl.cpp
  Unity_ScaleformStatsEtAl.cpp
  Unity_UnPawnEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\MU2Game\Src\MU2UIHUD.cpp(753): warning C5205: 删除具有非虚拟析构函数的抽象类 "FMU2SelectDialogueFunctor" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\MU2Game\Inc\MU2CommandFunctor.h(662): note: 参见“FMU2SelectDialogueFunctor”的声明
  Unity_tbbparallelforinterfaceEtAl.cpp
  Unity_XAudio2SourceEtAl.cpp
  Unity_FFileManagerNetworkEtAl.cpp
  Unity_AnimationUtilsEtAl.cpp
  Unity_UnWorldEtAl.cpp
  Unity_D3D11ViewportEtAl.cpp
  Unity_RHI_ShaderDescsEtAl.cpp
  Unity_NvApexDestructibleEtAl.cpp
F:\Release_Branch\Client\MU2\Development\Src\Engine\Debugger\UnDebuggerCore.cpp(156): warning C5205: 删除具有非虚拟析构函数的抽象类 "UDebuggerInterface" 将导致未定义的行为
  F:\Release_Branch\Client\MU2\Development\Src\Engine\Debugger\UnDebuggerInterface.h(43): note: 参见“UDebuggerInterface”的声明
  Unity_UnParticleComponentsEtAl.cpp
  Unity_ParticleTrail2EmitterInstanceEtAl.cpp
  Unity_UnLinkedObjDrawUtilsEtAl.cpp
  Unity_UnSkeletalAnimEtAl.cpp
  Unity_UnPhysAssetToolsEtAl.cpp
  Unity_UnSkeletalComponentEtAl.cpp
  Unity_UnAsyncLoadingEtAl.cpp
  Unity_ParticleModules_LocationEtAl.cpp
  Unity_UnPhysComponentEtAl.cpp
  Unity_UnNavigationMeshEtAl.cpp
  Unity_UnPatchCommandletsEtAl.cpp
  Unity_MaterialSharedEtAl.cpp
  Unity_ConsoleManagerEtAl.cpp
  Unity_SavePackageEtAl.cpp
  Unity_UnCorScEtAl.cpp
  Unity_UnMathEtAl.cpp
  Unity_UnObjEtAl.cpp
  Unity_UnPropEtAl.cpp
  Unity_SwarmInterfaceEtAl.cpp
  OpenAutomate.c
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2602): error C2118: 负下标
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2602): error C2148: 数组的总大小不得超过 0x7fffffff 字节
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2757): error C2369: “__C_ASSERT__”: 重定义；不同的下标
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2602): note: 参见“__C_ASSERT__”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2810): error C2369: “__C_ASSERT__”: 重定义；不同的下标
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2602): note: 参见“__C_ASSERT__”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(7111): error C2369: “__C_ASSERT__”: 重定义；不同的下标
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2602): note: 参见“__C_ASSERT__”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(8487): error C2369: “__C_ASSERT__”: 重定义；不同的下标
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2602): note: 参见“__C_ASSERT__”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(13525): error C2369: “__C_ASSERT__”: 重定义；不同的下标
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2602): note: 参见“__C_ASSERT__”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(14382): error C2369: “__C_ASSERT__”: 重定义；不同的下标
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2602): note: 参见“__C_ASSERT__”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(19229): error C2369: “__C_ASSERT__”: 重定义；不同的下标
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2602): note: 参见“__C_ASSERT__”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(20992): error C2369: “__C_ASSERT__”: 重定义；不同的下标
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2602): note: 参见“__C_ASSERT__”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2597): error C2338: static_assert failed: 'Windows headers require the default packing option. Changing this can lead to memory corruption. This diagnostic can be disabled by building with WINDOWS_IGNORE_PACKING_MISMATCH defined.'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(13585): warning C4121: “JOBOBJECT_IO_RATE_CONTROL_INFORMATION_NATIVE_V2”: 封装要区分成员对齐方式
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(13601): warning C4121: “JOBOBJECT_IO_RATE_CONTROL_INFORMATION_NATIVE_V3”: 封装要区分成员对齐方式
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(18253): warning C4121: “_POWER_SESSION_RIT_STATE”: 封装要区分成员对齐方式
EXEC : UBT error : Failed to produce item: F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.exe
EXEC : UBT error : Failed to produce item: F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.lib
EXEC : UBT error : Failed to produce item: F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.exp
EXEC : UBT error : Failed to produce item: F:\Release_Branch\Client\MU2\Binaries\Win32\MU2Game.pdb
  Could not connect to database.
  [1:50] UBT execution time: 117.79 seconds, 0.00 seconds linking
EXEC : warning : Communicating with the Database took 4.940585 seconds.
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.MakeFile.Targets(45,5): error MSB3073: 命令“@cd ..
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.MakeFile.Targets(45,5): error MSB3073: @call Targets/Build.bat MU2Game Win32 Release -vs2015 -DEFINE WITH_STEAMWORKS=1 -DEFINE WITH_GAMECENTER=0 -DEFINE WITH_PANORAMA=0 -DEFINE WITH_UE3_NETWORKING=1 -output "../../Binaries/Win32/MU2Game.exe"”已退出，代码为 1。
