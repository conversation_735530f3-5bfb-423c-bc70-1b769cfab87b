﻿  Creating ..\..\lib\vc_lib\Win32\mswd\wx\msw\rcdefs.h
  genrcdefs.h
  Creating ..\..\lib\vc_lib\Win32\mswd\wx\setup.h
  已复制         1 个文件。
cl : 命令行  warning D9035: “Gm”选项已否决，并将在将来的版本中移除
  regfree.c
  regexec.c
  regerror.c
  regcomp.c
  正在生成代码...
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppBuild.targets(1564,5): warning MSB8012: TargetPath(F:\Release_Branch\Client\MU2\Development\External\wxWidgets\lib\vc_lib\Win32\wxregex.lib) 与 Library 的 OutputFile 属性值(F:\Release_Branch\Client\MU2\Development\External\wxWidgets\lib\vc_lib\Win32\wxregexd.lib)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Lib.OutputFile) 中指定的值匹配。
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppBuild.targets(1566,5): warning MSB8012: TargetName(wxregex) 与 Library 的 OutputFile 属性值(wxregexd)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Lib.OutputFile) 中指定的值匹配。
  wx_wxregex.vcxproj -> F:\Release_Branch\Client\MU2\Development\External\wxWidgets\lib\vc_lib\Win32\wxregex.lib
