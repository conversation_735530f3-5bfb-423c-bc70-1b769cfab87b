﻿/**
 * Copyright 1998-2012 Epic Games, Inc. All Rights Reserved.
 */

using System;
using System.Collections.Generic;
using System.Text;

namespace UnrealBuildTool
{
    class UE3BuildMU2Game : UE3BuildGame
    {
        /** Returns the singular name of the game being built ("ExampleGame", "UDKGame", etc) */
        public string GetGameName()
        {
            return "MU2Game";
        }

        /** Returns a subplatform (e.g. dll) to disambiguate object files */
        public string GetSubPlatform()
        {
            return ("");
        }

        /** Get the desired OnlineSubsystem. */
        public string GetDesiredOnlineSubsystem(CPPEnvironment CPPEnv, UnrealTargetPlatform Platform)
        {
            return ("Steamworks");
        }

        /** Returns true if the game wants to have PC ES2 simulator (ie ES2 Dynamic RHI) enabled */
        public bool ShouldCompileES2()
        {
// WJC_VS2015
            return false;
            //return true;
        }

        /** Returns whether PhysX should be compiled on mobile platforms */
        public bool ShouldCompilePhysXMobile()
        {
            return UE3BuildConfiguration.bCompilePhysXWithMobile;
        }

        /** Allows the game add any global environment settings before building */
        public void GetGameSpecificGlobalEnvironment(CPPEnvironment GlobalEnvironment, UnrealTargetPlatform Platform)
        {

        }

        /** Allows the game to add any Platform/Configuration environment settings before building */
        public void GetGameSpecificPlatformConfigurationEnvironment(CPPEnvironment GlobalEnvironment, LinkEnvironment FinalLinkEnvironment)
        {

        }

        /** Returns the xex.xml file for the given game */
        public FileItem GetXEXConfigFile()
        {
            return null;
        }

        /** Allows the game to add any additional environment settings before building */
        public void SetUpGameEnvironment(CPPEnvironment GameCPPEnvironment, LinkEnvironment FinalLinkEnvironment, List<UE3ProjectDesc> GameProjects)
        {
            GameCPPEnvironment.IncludePaths.Add("MU2Game/Inc");
            if (UE3BuildConfiguration.bVS2015)
            {
                GameProjects.Add(new UE3ProjectDesc("MU2Game/Mu2Game.vcxproj"));
            }
            else if (UE3BuildConfiguration.bVS2013)
            {
                GameProjects.Add(new UE3ProjectDesc("MU2Game/Mu2Game_PcOnly_vs2013.vcxproj"));
            }
            else
            {
                GameProjects.Add(new UE3ProjectDesc("MU2Game/Mu2Game.vcxproj"));
            }

            SetUpWindowsDependencies(GameCPPEnvironment, FinalLinkEnvironment);

            if (UE3BuildConfiguration.bBuildEditor &&
                (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32 || GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64))
            {
                if (UE3BuildConfiguration.bVS2015)
                {
                    GameProjects.Add(new UE3ProjectDesc("MU2Editor/Mu2Editor.vcxproj"));
                }
                else if (UE3BuildConfiguration.bVS2013)
                {
                    GameProjects.Add(new UE3ProjectDesc("MU2Editor/Mu2Editor_PcOnly_vs2013.vcxproj"));
                }
                else
                {
                    GameProjects.Add(new UE3ProjectDesc("MU2Editor/Mu2Editor.vcxproj"));
                }
                GameCPPEnvironment.IncludePaths.Add("MU2Editor/Inc");
            }

            GameCPPEnvironment.Definitions.Add("GAMENAME=MU2GAME");
        }

        void SetUpWindowsDependencies(CPPEnvironment GameCPPEnvironment, LinkEnvironment FinalLinkEnvironment)
        {
            GameCPPEnvironment.IncludePaths.Add("../../../../");        // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Shared");  // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Shared/Scripts"); // client inc
            GameCPPEnvironment.IncludePaths.Add("../../../../server/Development"); // client inc

            GameCPPEnvironment.IncludePaths.Add("../../../../Vendor");  // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Vendor/boost");  // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Vendor/jsoncpp/include");  // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Vendor/log4cplus/include");

            // common lib
            FinalLinkEnvironment.AdditionalLibraries.Add("ws2_32.lib");  // socket lib
            FinalLinkEnvironment.AdditionalLibraries.Add("mswsock.lib");  // socket lib

            if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
            {
                // 雀荤 郴何 农贰矫 府器磐
                FinalLinkEnvironment.AdditionalLibraries.Add("../../Binaries/Win32/CrashReporter_r.lib");  // Crash Reporter lib
            }
            else
            {
                // 雀荤 郴何 农贰矫 府器磐
                FinalLinkEnvironment.AdditionalLibraries.Add("../../Binaries/Win64/CrashReporter_r.lib");  // Crash Reporter lib
            }

            string MathLibPath = "../../../../Shared/Lib/";
            string CoreLibPath = "../../../../Shared/Lib/";
            string SharedLibPath = "../../../../Shared/Lib/";
            string NetworkLibPath = "../../../../Shared/Lib/";
            string CryptLibPath = "../../../../Vendor/Lib/";
            string Log4LibPath = "../../../../Vendor/Lib/";
            string JsonLibPath = "../../../../Vendor/Lib/";
            string BoostLibPath = "../../../../Vendor/boost/stage/lib/";
            string GameGuardLibPath = "../External/GameGuard/";

            if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
            {
                CryptLibPath += "x86/";
                Log4LibPath += "x86/";
                JsonLibPath += "x86/";
                BoostLibPath += "x86/";
                GameGuardLibPath += "x86/";
            }
            else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
            {
                CryptLibPath += "x64/";
                Log4LibPath += "x64/";
                JsonLibPath += "x64/";
                BoostLibPath += "x64/";
                MathLibPath += "x64/";
                CoreLibPath += "x64/";
                SharedLibPath += "x64/";
                GameGuardLibPath += "x64/";
            }

            if (GameCPPEnvironment.TargetConfiguration == CPPTargetConfiguration.Debug)
            {
// WJC_VS2015
                if (UE3BuildConfiguration.bVS2015)
                {
                    MathLibPath += "Math_ClientD.lib";
                    CoreLibPath += "Core_ClientD.lib";
                    SharedLibPath += "Shared_ClientD.lib";
                    CryptLibPath += "cryptlib_md_d.lib";
                    Log4LibPath += "log4cplusUD.lib";
                    JsonLibPath += "lib_json_d.lib";

                    if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
                    {
                        NetworkLibPath += "ClientNetworkD.lib";
                    }
                    else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
                    {
                        NetworkLibPath += "ClientNetwork_x64D.lib";
                    }
                }
// WJC_VS2013
                else if (UE3BuildConfiguration.bVS2013)
                {
                    MathLibPath += "Math_2013_ClientD.lib";
                    CoreLibPath += "Core_2013_ClientD.lib";
                    SharedLibPath += "Shared_2013_ClientD.lib";
                    CryptLibPath += "cryptlib_2013_md_d.lib";
                    Log4LibPath += "log4cplusUD.lib";
                    JsonLibPath += "lib_json_d.lib";

                    if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
                    {
                        NetworkLibPath += "ClientNetwork_2013D.lib";
                    }
                    else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
                    {
                        NetworkLibPath += "ClientNetwork_2013_x64D.lib";
                    }
                }
                else
                {
                    MathLibPath += "Math_ClientD.lib";
                    CoreLibPath += "Core_ClientD.lib";
                    SharedLibPath += "Shared_ClientD.lib";
                    CryptLibPath += "cryptlib_md_d.lib";
                    Log4LibPath += "log4cplusUD.lib";
                    JsonLibPath += "lib_json_d.lib";

                    if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
                    {
                        NetworkLibPath += "ClientNetworkD.lib";
                    }
                    else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
                    {
                        NetworkLibPath += "ClientNetwork_64D.lib";
                    }
                }
            }
            else
            {
                if (UE3BuildConfiguration.bVS2015)
                {
                    MathLibPath += "Math_Client.lib";
                    CoreLibPath += "Core_Client.lib";
                    SharedLibPath += "Shared_Client.lib";
                    CryptLibPath += "cryptlib_md.lib";
                    Log4LibPath += "log4cplusU.lib";
                    JsonLibPath += "lib_json.lib";

                    if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
                    {
                        NetworkLibPath += "ClientNetwork.lib";
                    }
                    else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
                    {
                        NetworkLibPath += "ClientNetwork_x64.lib";
                    }
                }
                else if (UE3BuildConfiguration.bVS2013)
                {
                    MathLibPath += "Math_2013_Client.lib";
                    CoreLibPath += "Core_2013_Client.lib";
                    SharedLibPath += "Shared_2013_Client.lib";
                    CryptLibPath += "cryptlib_2013_md.lib";
                    Log4LibPath += "log4cplusU.lib";
                    JsonLibPath += "lib_json.lib";

                    if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
                    {
                        NetworkLibPath += "ClientNetwork_2013.lib";
                    }
                    else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
                    {
                        NetworkLibPath += "ClientNetwork_2013_x64.lib";
                    }
                }
                else
                {
                    MathLibPath += "Math_Client.lib";
                    CoreLibPath += "Core_Client.lib";
                    SharedLibPath += "Shared_Client.lib";
                    CryptLibPath += "cryptlib_md.lib";
                    Log4LibPath += "log4cplusU.lib";
                    JsonLibPath += "lib_json.lib";

                    if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
                    {
                        NetworkLibPath += "ClientNetwork.lib";
                    }
                    else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
                    {
                        NetworkLibPath += "ClientNetwork_64.lib";
                    }
                }
            }

            FinalLinkEnvironment.AdditionalLibraries.Add(MathLibPath);
            FinalLinkEnvironment.AdditionalLibraries.Add(CoreLibPath); 
            FinalLinkEnvironment.AdditionalLibraries.Add(NetworkLibPath);
            FinalLinkEnvironment.AdditionalLibraries.Add(SharedLibPath);
            FinalLinkEnvironment.AdditionalLibraries.Add(CryptLibPath);
            FinalLinkEnvironment.AdditionalLibraries.Add(Log4LibPath);
            FinalLinkEnvironment.AdditionalLibraries.Add(JsonLibPath);
            FinalLinkEnvironment.LibraryPaths.Add(BoostLibPath);            // date_time俊 措茄 狼粮捞 asio锭巩俊 眠啊凳
            FinalLinkEnvironment.LibraryPaths.Add(GameGuardLibPath);
        }

        void SetUpMobileDependencies(CPPEnvironment GameCPPEnvironment, LinkEnvironment FinalLinkEnvironment)
        {
            GameCPPEnvironment.IncludePaths.Add("../../../../");        // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../NNet");
            GameCPPEnvironment.IncludePaths.Add("../../../../NNet/asio/include");
            GameCPPEnvironment.IncludePaths.Add("../../../../Shared/Protocol/");
            GameCPPEnvironment.IncludePaths.Add("../../../../Shared/Protocol/Common");

            GameCPPEnvironment.IncludePaths.Add("../../../../Vendor");  // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Vendor/boost");  // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Vendor/jsoncpp/include");  // server inc

            GameCPPEnvironment.Definitions.Add("ASIO_SEPARATE_COMPILATION");
            GameCPPEnvironment.Definitions.Add("ASIO_STAND_ALONE");
            GameCPPEnvironment.Definitions.Add("BOOST_ALL_NO_LIB");

            FinalLinkEnvironment.LibraryPaths.Add("../../../../NNet/lib/armeabi-v7a");
            FinalLinkEnvironment.AdditionalLibraries.Add("nnet");
            // FinalLinkEnvironment.AdditionalLibraries.Add("cryptopp");
        }
    }
}
