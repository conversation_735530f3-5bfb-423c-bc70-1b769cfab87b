#ifndef _MU2_SHARED_VERSION_H_
#define _MU2_SHARED_VERSION_H_

// Removed #pragma once for resource compiler compatibility
// 由于资源编译器兼容性问题，移除了 #pragma once
// #pragma once

// #include "LocaleDef.h.sample" // [TODO] Should be fixed as "LocaleDef.h" later
// // [待修复] 应改为包含正式文件 "LocaleDef.h"

// Always define version info (remove MU2_KOREA conditional for resource compiler)
// 始终定义版本信息（去除 MU2_KOREA 条件编译以兼容资源编译器）
// #if defined(MU2_KOREA)

// [MUSTBE VERSION UPDATE HERE] : comment by mook
// [此处必须进行版本更新] ：mook 的备注

#define MU2_MAJOR_VERSION 0
#define MU2_MINOR_VERSION 0
#define MU2_PATCH_VERSION 51    // 2019.01.21 정광진, 删除了成就(KILL_MONSTER/JOB_LIMIT_ACHIEVEMENT/SKILL_INDEX)

//#define BES_SPEED_HACK_TEST 

// Uncomment below to enable patch history definitions
// 取消下面注释可启用补丁版本历史记录
// #define __VersionUp_History

#ifdef __VersionUp_History

#undef MU2_PATCH_VERSION  // Prevent macro redefinition warning/error
// 取消之前定义，防止宏重定义警告或错误

// Version up history
// 版本更新历史（仅用于记录）

#define MU2_PATCH_VERSION 1     // 2016.10.31 by 정광진, 初始版本
#define MU2_PATCH_VERSION 2     // 删除 EREQ_GAME_BASE_TOWN_PORTAL, ERES_GAME_BASE_TOWN_PORTAL
#define MU2_PATCH_VERSION 3     // 清理未使用的事件枚举
#define MU2_PATCH_VERSION 4     // 更改中心包结构为 PackUnpack
#define MU2_PATCH_VERSION 5     // 修改默认时间结构为 SqlDateTimeEx
#define MU2_PATCH_VERSION 6     // 整理 ProtocolRange
#define MU2_PATCH_VERSION 7     // 整理 ProtocolGame
#define MU2_PATCH_VERSION 8     // 安全性问题，移除 charId
#define MU2_PATCH_VERSION 9     // 清理未使用的事件
#define MU2_PATCH_VERSION 10    // 进一步清理事件，验证协议方向
#define MU2_PATCH_VERSION 11    // 拆分 zone server loopback，重新添加 EReqPVEMissionMapExit（客户端仍使用）
#define MU2_PATCH_VERSION 12    // 拆分 world server loopback
#define MU2_PATCH_VERSION 13    // 整理 ID 结构（WorldId, ServerId, 等）
#define MU2_PATCH_VERSION 14    // ItemAppearanceInfo 添加期限属性
#define MU2_PATCH_VERSION 15    // 添加超越强化信息
#define MU2_PATCH_VERSION 16    // 加载流程重构
#define MU2_PATCH_VERSION 17    // 修复新建角色技能快捷栏 bug，整理 SP
#define MU2_PATCH_VERSION 18    // 增加事件背包和现金保管箱最后检查信息
#define MU2_PATCH_VERSION 19    // 添加 ItemData Block
#define MU2_PATCH_VERSION 20    // 添加 MyInfoCharacter 的 membershipInfo
#define MU2_PATCH_VERSION 21    // 删除 MyInfoCharacter 的 membershipInfo
#define MU2_PATCH_VERSION 22    // 登录流程重构
#define MU2_PATCH_VERSION 23    // ENtfMembershipSettingInfo 添加新变量
#define MU2_PATCH_VERSION 24    // 添加骑士团相关包
#define MU2_PATCH_VERSION 25    // 框架加密模块调整，网络 Send 调整
#define MU2_PATCH_VERSION 26    // 添加骑士团跟随信息包
#define MU2_PATCH_VERSION 27    // 加载角色时加载跟随值
#define MU2_PATCH_VERSION 28    // 添加渠道信息
#define MU2_PATCH_VERSION 29    // 框架网络重构，WorldRelay 重构，客户端 Session 管理重构
#define MU2_PATCH_VERSION 30    // 删除固定选项包
#define MU2_PATCH_VERSION 31    // EResResetTrascendStone 发送 ItemData
#define MU2_PATCH_VERSION 32    // 修改交易所打开 UI 时的货币信息
#define MU2_PATCH_VERSION 33    // 删除 Event 中的 src，扩展 dst
#define MU2_PATCH_VERSION 34    // 将 dst 改为 ServerId 类型
#define MU2_PATCH_VERSION 35    // 将 ServerId 类型从 16bit 改为 32bit
#define MU2_PATCH_VERSION 36    // 增加客户端加载阶段
#define MU2_PATCH_VERSION 37    // 添加客户端 debug 消息包，移除 invasion 相关结构
#define MU2_PATCH_VERSION 38    // 增加排队系统相关逻辑
#define MU2_PATCH_VERSION 39    // 交易中介所，物品价格，名字搜索修改
#define MU2_PATCH_VERSION 40    // 交易中介所：最新价格、批量接收
#define MU2_PATCH_VERSION 41    // Item 重构
#define MU2_PATCH_VERSION 42    // 更改混沌城堡数据包数据类型
#define MU2_PATCH_VERSION 43    // 增加“一键开启”魔方功能
#define MU2_PATCH_VERSION 44    // 装备强化折扣与 GMS 联动
#define MU2_PATCH_VERSION 45    // 增加装备强化折扣种类（红宝石/非红宝石）
#define MU2_PATCH_VERSION 46    // 整理 foothold
#define MU2_PATCH_VERSION 47    // 转生第一阶段
#define MU2_PATCH_VERSION 48    // Talisman 转移功能
#define MU2_PATCH_VERSION 49    // 交易中介所改进
#define MU2_PATCH_VERSION 50    // 装备 on/off 状态支持

#endif // __VersionUp_History

// #include "Features.h"  // Features to be included by version
// // 版本相关的功能模块

#endif // _MU2_SHARED_VERSION_H_
