﻿#ifndef _MU2_SHARED_VERSION_H_
#define _MU2_SHARED_VERSION_H_

#pragma once

#include "LocaleDef.h.sample" // [TODO] Should be fixed as 'LocaleDef.h" later

#if defined(MU2_KOREA)

// [MUSTBE VERSION UPDATE HERE] : comment by mook

#define MU2_MAJOR_VERSION 0
#define MU2_MINOR_VERSION 0
#define MU2_PATCH_VERSION 51			// 2019.01.21 정광진, 업적(KILL_MONSTER/JOB_LIMIT_ACHIEVEMENT/SKILL_INDEX 제거)


//#define BES_SPEED_HACK_TEST 

#if __VersionUp_History
// 버전업 기록

#define MU2_PATCH_VERSION 1				// 2016.10.31 by 정광진, 최초버전
#define MU2_PATCH_VERSION 2				// 2016.10.31 by 정광진, EREQ_GAME_BASE_TOWN_PORTAL, ERES_GAME_BASE_TOWN_PORTAL 삭제
#define MU2_PATCH_VERSION 3				// 2016.11.02 13:33 by 정광진, 사용하지 않는 이벤트 열거체 정리
#define MU2_PATCH_VERSION 4				// 2016.11.09 10:01 by 정광진, 센터패킷 PackUnpack으로 변경
#define MU2_PATCH_VERSION 5				// 2016.11.30 15:19 by 정광진, 패킷에 사용하는 시간구조체 Default를 SqlDateTimeEx로 변경
#define MU2_PATCH_VERSION 6				// 2016.12.08 12:08 by 정광진, ProtocolRange 정리중
#define MU2_PATCH_VERSION 7				// 2016.12.12 18:06 by 정광진, ProtocolGame 정리중
#define MU2_PATCH_VERSION 8				// 2016.12.15 14:20 by 정광진, 해킹이슈로 클라이언트와 서버간 통신에서 charId 제거작업중
#define MU2_PATCH_VERSION 9				// 2016.12.16 10:06 by 정광진, 프로토콜 정리중, 사용하지 않는 이벤트들 제거
#define MU2_PATCH_VERSION 10			// 2016.12.20 21:38 by 정광진, 프로토콜 정리중, 사용하지 않는 이벤트들 제거, 프로토콜 방향성 검증
#define MU2_PATCH_VERSION 11			// 2016.12.21 12:40 by 정광진, 존서버 루프백 이벤트 분리, EReqPVEMissionMapExit 다시 추가(클라이언트에서 사용해서 지웠다 추가함)
#define MU2_PATCH_VERSION 12			// 2016.12.21 16:47 by 정광진, 월드서버 루프백 이벤트 분리
#define MU2_PATCH_VERSION 13			// 2016.12.27 12:00 by 정광진, WorldId, ServerId, SessionKey, CharId, AccountId 소스 정리중
#define MU2_PATCH_VERSION 14			// 2016.01.06 8:53 이화영, ItemApperanceInfo에 기간제 정보 추가.
#define MU2_PATCH_VERSION 15			// 2017.01.13 이화영, 초월 강화 정보 추가.
#define MU2_PATCH_VERSION 16			// 2017.01.19 정광진, 로딩 리팩토링.
#define MU2_PATCH_VERSION 17			// 2017.01.20 정광진, 신규생성시, 스킬퀵슬롯 버그 수정, sp 정리
#define MU2_PATCH_VERSION 18			// 2017.02.01 홍원표, 이벤트 인벤토리 및 캐쉬 아이템 보관함 마지막 체크 정보 추가
#define MU2_PATCH_VERSION 19			// 2017.02.02 이화영, ItemData Block 추가.
#define MU2_PATCH_VERSION 20			// 2017.02.03 홍원표, MyInfoCharacter 정보에 membershipInfo 정보 추가
#define MU2_PATCH_VERSION 21			// 2017.02.06 홍원표, MyInfoCharacter 정보에 membershipInfo 정보 삭제
#define MU2_PATCH_VERSION 22			// 2017.02.08 정광진, 로그인과정 리팩토링
#define MU2_PATCH_VERSION 23			// 2017.02.16 홍원표, ENtfMembershipSettingInfo에 변수 추가
#define MU2_PATCH_VERSION 24			// 2017.02.21 이화영, 기사단 관련 패킷 추가.
#define MU2_PATCH_VERSION 25			// 2017.02.28 정광진, 프레임워크 암호화 관련모듈 수정, 네트워크 Send 수정
#define MU2_PATCH_VERSION 26			// 2017.03.06 By 이화영, 기사단 팔로우 정보 보내는 패킷 추가.
#define MU2_PATCH_VERSION 27			// 2017.03.15 By 이화영, 월드에서 케릭터 로드시 팔로우 값도 로드 하도록 수정
#define MU2_PATCH_VERSION 28			// 2017.04.10 By 홍원표, 채널링 정보 추가
#define MU2_PATCH_VERSION 29			// 2017.04.10 21:35 By 정광진, 프레임워크 네트워크 리팩토링, WorldRelay 리팩토링, Clinet 세션관리 리팩토링
#define MU2_PATCH_VERSION 30			// 2017.04.19 by 이화영 고정옵션 패킷 제거.
#define MU2_PATCH_VERSION 31			// 2017.05.24. 클라이언트 요청으로 EResResetTrascendStone ItemData 보내도록 수정
#define MU2_PATCH_VERSION 32			// 2017.08.01. 거래소 사용 재화 관련된 거래소 OPEN UI 정보 수정
#define MU2_PATCH_VERSION 33			// 2017.08.28. 홍원표, Event에서 src 삭제 및 dst 확장
#define MU2_PATCH_VERSION 34			// 2017.08.28. 홍원표, dst를 ServerId 타입으로 변경
#define MU2_PATCH_VERSION 35			// 2017.08.28. 홍원표, dst를 ServerId 타입을 16bit에서 32비트로 변경
#define MU2_PATCH_VERSION 36			// 2017.10.10. 정광진, 클라이언트 로딩 단계 추가
#define MU2_PATCH_VERSION 37			// 2017.10.16. 정광진, 클라이언트 디버그메세지 패킷 추가, invasion 관련 구조체/패팃 제거
#define MU2_PATCH_VERSION 38			// 2017.11.01 정광진, 대기열관련 추가작업
#define MU2_PATCH_VERSION 39			// 2018.01.29 정광진, 거래중개소, 아이템시세, 이름검색수정
#define MU2_PATCH_VERSION 40			// 2018.03.14 정광진, 거래중개소, 최근시세, 한번에 받기, __ExchangeShop_Jason_180314__
#define MU2_PATCH_VERSION 41			// 2018.03.23 정광진, 아이템리팩토링
#define MU2_PATCH_VERSION 42			// 2018.03.29 봉은석, 카오스캐슬관련 패킷데이터타입 변경
#define MU2_PATCH_VERSION 43			// 2018.04.03 정광진, 큐브 한번에 열기
#define MU2_PATCH_VERSION 44			// 2018.05.29 정광진, 아이템강화 할인률 GMS 연동
#define MU2_PATCH_VERSION 45			// 2018.06.05 정광진, 아이템강화 할인률, 할인항목 추가(레드젠, 비레드젠)
#define MU2_PATCH_VERSION 46			// 2018.06.22 정광진, 풋홀드정리
#define MU2_PATCH_VERSION 47			// 2018.07.10 정광진, 환생1차
#define MU2_PATCH_VERSION 48			// 2018.09.13 정광진, 탈리스만 이전
#define MU2_PATCH_VERSION 49			// 2018.11.28 정광진, 거래중개소
#define MU2_PATCH_VERSION 50			// 2018.12.03 이화영, 장비 on, off
#endif

#endif  // MU2_KOREA





#include "Features.h"  // Features to be included by version

#endif  // _MU2_SHARED_VERSION_H_
