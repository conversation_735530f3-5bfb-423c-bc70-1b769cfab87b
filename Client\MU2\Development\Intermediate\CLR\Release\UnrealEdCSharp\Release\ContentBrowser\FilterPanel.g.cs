﻿#pragma checksum "ContentBrowser\FilterPanel.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B7A3CE0BECAD7C2AFCF245E9AF3E4F053926B65E"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using ContentBrowser;
using CustomControls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ContentBrowser {
    
    
    /// <summary>
    /// FilterPanel
    /// </summary>
    public partial class FilterPanel : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 79 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SlateBorder mExpanderButtonBackground;
        
        #line default
        #line hidden
        
        
        #line 80 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton mShowExpandedButton;
        
        #line default
        #line hidden
        
        
        #line 84 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SlateBorder mClearButtonBackground;
        
        #line default
        #line hidden
        
        
        #line 85 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button mClearFilterButton;
        
        #line default
        #line hidden
        
        
        #line 90 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SlateBorder mSearchBorder;
        
        #line default
        #line hidden
        
        
        #line 93 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button mShowSearchFieldsButton;
        
        #line default
        #line hidden
        
        
        #line 96 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup mSearchFieldsList;
        
        #line default
        #line hidden
        
        
        #line 105 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox mShouldSearchField_Name;
        
        #line default
        #line hidden
        
        
        #line 106 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox mShouldSearchField_Path;
        
        #line default
        #line hidden
        
        
        #line 107 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox mShouldSearchField_Tags;
        
        #line default
        #line hidden
        
        
        #line 108 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox mShouldSearchField_Type;
        
        #line default
        #line hidden
        
        
        #line 113 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox mAllAnySwitch;
        
        #line default
        #line hidden
        
        
        #line 121 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton mQuarantineMode;
        
        #line default
        #line hidden
        
        
        #line 128 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.Autocomplete mSearchFieldAutocomplete;
        
        #line default
        #line hidden
        
        
        #line 131 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.UnrealTextBox mSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 149 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.CellSizer mExpandingPanel;
        
        #line default
        #line hidden
        
        
        #line 166 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ColumnDefinition mTagTier0_GridColumn;
        
        #line default
        #line hidden
        
        
        #line 167 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ColumnDefinition mTagTier1_GridColumn;
        
        #line default
        #line hidden
        
        
        #line 168 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ColumnDefinition mTagTier2_GridColumn;
        
        #line default
        #line hidden
        
        
        #line 174 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.SlateBorder mStatusFilterBorder;
        
        #line default
        #line hidden
        
        
        #line 183 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mInUseCurrentLevel;
        
        #line default
        #line hidden
        
        
        #line 184 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mInUseLoadedLevels;
        
        #line default
        #line hidden
        
        
        #line 185 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mInUseVisibleLevels;
        
        #line default
        #line hidden
        
        
        #line 186 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mInUseOff;
        
        #line default
        #line hidden
        
        
        #line 193 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mShowTaggedOnly;
        
        #line default
        #line hidden
        
        
        #line 194 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mShowUntaggedOnly;
        
        #line default
        #line hidden
        
        
        #line 195 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mShowTaggedAndUntagged;
        
        #line default
        #line hidden
        
        
        #line 202 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mShowLoadedOnly;
        
        #line default
        #line hidden
        
        
        #line 203 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mShowUnloadedOnly;
        
        #line default
        #line hidden
        
        
        #line 204 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mShowLoadedAndUnloaded;
        
        #line default
        #line hidden
        
        
        #line 211 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox mShowRecentOnly;
        
        #line default
        #line hidden
        
        
        #line 212 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox mShowQuarantinedOnly;
        
        #line default
        #line hidden
        
        
        #line 213 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox mShowFlattenedTextures;
        
        #line default
        #line hidden
        
        
        #line 223 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.TypeFilterTier mObjectTypeFilterTier;
        
        #line default
        #line hidden
        
        
        #line 226 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.TagFilterTier mTagFilterTier0;
        
        #line default
        #line hidden
        
        
        #line 229 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.TagFilterTier mTagFilterTier1;
        
        #line default
        #line hidden
        
        
        #line 232 "ContentBrowser\FilterPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.TagFilterTier mTagFilterTier2;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UnrealEdCSharp;component/contentbrowser/filterpanel.xaml", System.UriKind.Relative);
            
            #line 1 "ContentBrowser\FilterPanel.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.mExpanderButtonBackground = ((CustomControls.SlateBorder)(target));
            return;
            case 2:
            this.mShowExpandedButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 3:
            this.mClearButtonBackground = ((CustomControls.SlateBorder)(target));
            return;
            case 4:
            this.mClearFilterButton = ((System.Windows.Controls.Button)(target));
            return;
            case 5:
            this.mSearchBorder = ((CustomControls.SlateBorder)(target));
            return;
            case 6:
            this.mShowSearchFieldsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 7:
            this.mSearchFieldsList = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 8:
            this.mShouldSearchField_Name = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.mShouldSearchField_Path = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.mShouldSearchField_Tags = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.mShouldSearchField_Type = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.mAllAnySwitch = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.mQuarantineMode = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 14:
            this.mSearchFieldAutocomplete = ((CustomControls.Autocomplete)(target));
            return;
            case 15:
            this.mSearchTextBox = ((CustomControls.UnrealTextBox)(target));
            return;
            case 16:
            this.mExpandingPanel = ((CustomControls.CellSizer)(target));
            return;
            case 17:
            this.mTagTier0_GridColumn = ((System.Windows.Controls.ColumnDefinition)(target));
            return;
            case 18:
            this.mTagTier1_GridColumn = ((System.Windows.Controls.ColumnDefinition)(target));
            return;
            case 19:
            this.mTagTier2_GridColumn = ((System.Windows.Controls.ColumnDefinition)(target));
            return;
            case 20:
            this.mStatusFilterBorder = ((CustomControls.SlateBorder)(target));
            return;
            case 21:
            this.mInUseCurrentLevel = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 22:
            this.mInUseLoadedLevels = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 23:
            this.mInUseVisibleLevels = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 24:
            this.mInUseOff = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 25:
            this.mShowTaggedOnly = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 26:
            this.mShowUntaggedOnly = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 27:
            this.mShowTaggedAndUntagged = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 28:
            this.mShowLoadedOnly = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 29:
            this.mShowUnloadedOnly = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 30:
            this.mShowLoadedAndUnloaded = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 31:
            this.mShowRecentOnly = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 32:
            this.mShowQuarantinedOnly = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 33:
            this.mShowFlattenedTextures = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 34:
            this.mObjectTypeFilterTier = ((ContentBrowser.TypeFilterTier)(target));
            return;
            case 35:
            this.mTagFilterTier0 = ((ContentBrowser.TagFilterTier)(target));
            return;
            case 36:
            this.mTagFilterTier1 = ((ContentBrowser.TagFilterTier)(target));
            return;
            case 37:
            this.mTagFilterTier2 = ((ContentBrowser.TagFilterTier)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

