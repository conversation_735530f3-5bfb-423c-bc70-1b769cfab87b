﻿#pragma checksum "ContentBrowser\SourcesPanel.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FE0F49860F2087BC7BEB81141ADE3634B8102E92"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using ContentBrowser;
using CustomControls;
using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ContentBrowser {
    
    
    /// <summary>
    /// SourcesPanel
    /// </summary>
    public partial class SourcesPanel : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 3 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ContentBrowser.SourcesPanel This;
        
        #line default
        #line hidden
        
        
        #line 230 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.NameEntryPrompt mAddSharedCollectionPromptPopup;
        
        #line default
        #line hidden
        
        
        #line 234 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.NameEntryPrompt mAddPrivateCollectionPromptPopup;
        
        #line default
        #line hidden
        
        
        #line 238 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.YesNoPrompt mDestroySharedCollectionPrompt;
        
        #line default
        #line hidden
        
        
        #line 246 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.YesNoPrompt mDestroyPrivateCollectionPrompt;
        
        #line default
        #line hidden
        
        
        #line 254 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.NameEntryPrompt mRenameSharedCollectionPromptPopup;
        
        #line default
        #line hidden
        
        
        #line 258 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.NameEntryPrompt mRenamePrivateCollectionPromptPopup;
        
        #line default
        #line hidden
        
        
        #line 262 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.NameEntryPrompt mCreateSharedCopyOfSharedCollectionPromptPopup;
        
        #line default
        #line hidden
        
        
        #line 266 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.NameEntryPrompt mCreatePrivateCopyOfSharedCollectionPromptPopup;
        
        #line default
        #line hidden
        
        
        #line 270 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.NameEntryPrompt mCreateSharedCopyOfPrivateCollectionPromptPopup;
        
        #line default
        #line hidden
        
        
        #line 274 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.NameEntryPrompt mCreatePrivateCopyOfPrivateCollectionPromptPopup;
        
        #line default
        #line hidden
        
        
        #line 278 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.NameEntryPrompt mCreateSharedCopyOfLocalCollectionPromptPopup;
        
        #line default
        #line hidden
        
        
        #line 282 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.NameEntryPrompt mCreatePrivateCopyOfLocalCollectionPromptPopup;
        
        #line default
        #line hidden
        
        
        #line 286 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.YesNoPrompt mProceedWithAddToCollection;
        
        #line default
        #line hidden
        
        
        #line 293 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.YesNoPrompt mProceedWithRemoveFromCollection;
        
        #line default
        #line hidden
        
        
        #line 308 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnNewAsset;
        
        #line default
        #line hidden
        
        
        #line 309 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnImportAsset;
        
        #line default
        #line hidden
        
        
        #line 310 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOpenPackage;
        
        #line default
        #line hidden
        
        
        #line 315 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView m_SpecialCollectionsList;
        
        #line default
        #line hidden
        
        
        #line 372 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListViewItem mAllAssetsItem;
        
        #line default
        #line hidden
        
        
        #line 375 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.ExpandoPanel mExpandoPanel;
        
        #line default
        #line hidden
        
        
        #line 377 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.ExpandoSubpanel mSharedCollectionsSizer;
        
        #line default
        #line hidden
        
        
        #line 382 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button mAddSharedCollectionButton;
        
        #line default
        #line hidden
        
        
        #line 390 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button mRemoveSharedCollectionButton;
        
        #line default
        #line hidden
        
        
        #line 404 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView mSharedCollectionsList;
        
        #line default
        #line hidden
        
        
        #line 429 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.ExpandoSubpanel mPrivateCollectionsSizer;
        
        #line default
        #line hidden
        
        
        #line 434 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button mAddPrivateCollectionButton;
        
        #line default
        #line hidden
        
        
        #line 441 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button mRemovePrivateCollectionButton;
        
        #line default
        #line hidden
        
        
        #line 455 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView mPrivateCollectionsList;
        
        #line default
        #line hidden
        
        
        #line 480 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.ExpandoSubpanel mPackagesSizer;
        
        #line default
        #line hidden
        
        
        #line 488 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mPackageViewMode_Tree;
        
        #line default
        #line hidden
        
        
        #line 489 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton mPackageViewMode_List;
        
        #line default
        #line hidden
        
        
        #line 545 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid mPackageViewGrid;
        
        #line default
        #line hidden
        
        
        #line 552 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border mPackageFilter;
        
        #line default
        #line hidden
        
        
        #line 561 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CustomControls.UnrealTextBox mPackageFilterText;
        
        #line default
        #line hidden
        
        
        #line 565 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton mMatchAny;
        
        #line default
        #line hidden
        
        
        #line 568 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton mShowNonRecursive;
        
        #line default
        #line hidden
        
        
        #line 571 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton mShowDirtyOnly;
        
        #line default
        #line hidden
        
        
        #line 574 "ContentBrowser\SourcesPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton mShowCheckOutOnly;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UnrealEdCSharp;component/contentbrowser/sourcespanel.xaml", System.UriKind.Relative);
            
            #line 1 "ContentBrowser\SourcesPanel.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.This = ((ContentBrowser.SourcesPanel)(target));
            return;
            case 2:
            
            #line 171 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 172 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 174 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 175 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 177 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 178 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 180 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 181 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 183 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 184 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 186 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 187 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 189 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 190 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 192 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 193 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 195 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 196 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 198 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 199 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 204 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 205 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 207 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 208 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 210 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 211 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 213 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 214 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 216 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 217 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 220 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 220 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 221 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 221 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 222 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 222 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 223 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).CanExecute += new System.Windows.Input.CanExecuteRoutedEventHandler(this.CanExecuteMenuCommand);
            
            #line default
            #line hidden
            
            #line 223 "ContentBrowser\SourcesPanel.xaml"
            ((System.Windows.Input.CommandBinding)(target)).Executed += new System.Windows.Input.ExecutedRoutedEventHandler(this.ExecuteMenuCommand);
            
            #line default
            #line hidden
            return;
            case 21:
            this.mAddSharedCollectionPromptPopup = ((CustomControls.NameEntryPrompt)(target));
            return;
            case 22:
            this.mAddPrivateCollectionPromptPopup = ((CustomControls.NameEntryPrompt)(target));
            return;
            case 23:
            this.mDestroySharedCollectionPrompt = ((CustomControls.YesNoPrompt)(target));
            return;
            case 24:
            this.mDestroyPrivateCollectionPrompt = ((CustomControls.YesNoPrompt)(target));
            return;
            case 25:
            this.mRenameSharedCollectionPromptPopup = ((CustomControls.NameEntryPrompt)(target));
            return;
            case 26:
            this.mRenamePrivateCollectionPromptPopup = ((CustomControls.NameEntryPrompt)(target));
            return;
            case 27:
            this.mCreateSharedCopyOfSharedCollectionPromptPopup = ((CustomControls.NameEntryPrompt)(target));
            return;
            case 28:
            this.mCreatePrivateCopyOfSharedCollectionPromptPopup = ((CustomControls.NameEntryPrompt)(target));
            return;
            case 29:
            this.mCreateSharedCopyOfPrivateCollectionPromptPopup = ((CustomControls.NameEntryPrompt)(target));
            return;
            case 30:
            this.mCreatePrivateCopyOfPrivateCollectionPromptPopup = ((CustomControls.NameEntryPrompt)(target));
            return;
            case 31:
            this.mCreateSharedCopyOfLocalCollectionPromptPopup = ((CustomControls.NameEntryPrompt)(target));
            return;
            case 32:
            this.mCreatePrivateCopyOfLocalCollectionPromptPopup = ((CustomControls.NameEntryPrompt)(target));
            return;
            case 33:
            this.mProceedWithAddToCollection = ((CustomControls.YesNoPrompt)(target));
            return;
            case 34:
            this.mProceedWithRemoveFromCollection = ((CustomControls.YesNoPrompt)(target));
            return;
            case 35:
            this.btnNewAsset = ((System.Windows.Controls.Button)(target));
            return;
            case 36:
            this.btnImportAsset = ((System.Windows.Controls.Button)(target));
            return;
            case 37:
            this.btnOpenPackage = ((System.Windows.Controls.Button)(target));
            return;
            case 38:
            this.m_SpecialCollectionsList = ((System.Windows.Controls.ListView)(target));
            return;
            case 39:
            this.mAllAssetsItem = ((System.Windows.Controls.ListViewItem)(target));
            return;
            case 40:
            this.mExpandoPanel = ((CustomControls.ExpandoPanel)(target));
            return;
            case 41:
            this.mSharedCollectionsSizer = ((CustomControls.ExpandoSubpanel)(target));
            return;
            case 42:
            this.mAddSharedCollectionButton = ((System.Windows.Controls.Button)(target));
            return;
            case 43:
            this.mRemoveSharedCollectionButton = ((System.Windows.Controls.Button)(target));
            return;
            case 44:
            this.mSharedCollectionsList = ((System.Windows.Controls.ListView)(target));
            return;
            case 45:
            this.mPrivateCollectionsSizer = ((CustomControls.ExpandoSubpanel)(target));
            return;
            case 46:
            this.mAddPrivateCollectionButton = ((System.Windows.Controls.Button)(target));
            return;
            case 47:
            this.mRemovePrivateCollectionButton = ((System.Windows.Controls.Button)(target));
            return;
            case 48:
            this.mPrivateCollectionsList = ((System.Windows.Controls.ListView)(target));
            return;
            case 49:
            this.mPackagesSizer = ((CustomControls.ExpandoSubpanel)(target));
            return;
            case 50:
            this.mPackageViewMode_Tree = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 51:
            this.mPackageViewMode_List = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 52:
            this.mPackageViewGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 53:
            this.mPackageFilter = ((System.Windows.Controls.Border)(target));
            return;
            case 54:
            this.mPackageFilterText = ((CustomControls.UnrealTextBox)(target));
            return;
            case 55:
            this.mMatchAny = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 56:
            this.mShowNonRecursive = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 57:
            this.mShowDirtyOnly = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 58:
            this.mShowCheckOutOnly = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

